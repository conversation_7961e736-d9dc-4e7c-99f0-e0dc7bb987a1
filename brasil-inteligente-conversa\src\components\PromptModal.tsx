import { useState } from "react";
import { <PERSON>, <PERSON><PERSON>, <PERSON>, Star, Users, Tag, BookOpen, Zap, Share2 } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Textarea } from "@/components/ui/textarea";
import { Dialog, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import SocialShare from "./SocialShare";
import type { Prompt } from "@/data/prompts";

interface PromptModalProps {
  prompt: Prompt | null;
  isOpen: boolean;
  onClose: () => void;
}

const PromptModal = ({ prompt, isOpen, onClose }: PromptModalProps) => {
  const [copied, setCopied] = useState(false);
  const [customizedPrompt, setCustomizedPrompt] = useState("");

  if (!prompt) return null;

  // Generate a sample prompt text if not provided
  const promptText = prompt.prompt || generateSamplePrompt(prompt);

  const copyToClipboard = async (text: string) => {
    try {
      await navigator.clipboard.writeText(text);
      setCopied(true);
      setTimeout(() => setCopied(false), 2000);
    } catch (err) {
      console.error('Failed to copy: ', err);
    }
  };

  const getDifficultyColor = (difficulty: string) => {
    switch (difficulty) {
      case 'Iniciante': return 'bg-green-100 text-green-800';
      case 'Intermediário': return 'bg-yellow-100 text-yellow-800';
      case 'Avançado': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <div className="flex items-start justify-between">
            <div className="flex-1">
              <div className="flex items-center gap-2 mb-2">
                <Badge variant="secondary" className="text-xs">
                  {prompt.category}
                </Badge>
                <Badge className={getDifficultyColor(prompt.difficulty || 'Intermediário')}>
                  {prompt.difficulty || 'Intermediário'}
                </Badge>
              </div>
              <DialogTitle className="text-2xl font-bold text-gray-900 mb-2">
                {prompt.title}
              </DialogTitle>
              <p className="text-gray-600 text-base">
                {prompt.description}
              </p>
            </div>
          </div>
        </DialogHeader>

        <div className="space-y-6">
          {/* Stats */}
          <div className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
            <div className="flex items-center space-x-6">
              <div className="flex items-center space-x-2">
                <Star className="h-5 w-5 text-yellow-500 fill-current" />
                <span className="font-semibold">{prompt.rating}</span>
                <span className="text-gray-500 text-sm">avaliação</span>
              </div>
              <div className="flex items-center space-x-2">
                <Users className="h-5 w-5 text-blue-500" />
                <span className="font-semibold">{prompt.uses.toLocaleString()}</span>
                <span className="text-gray-500 text-sm">usos</span>
              </div>
            </div>
            <SocialShare 
              title={`${prompt.title} - Prompt Genius AI`}
              description={prompt.description}
              hashtags={["PromptGenius", "IA", "ChatGPT", prompt.category.replace(/\s+/g, '')]}
            />
          </div>

          {/* Tags */}
          {prompt.tags && prompt.tags.length > 0 && (
            <div>
              <h3 className="font-semibold text-gray-900 mb-3 flex items-center">
                <Tag className="h-4 w-4 mr-2" />
                Tags
              </h3>
              <div className="flex flex-wrap gap-2">
                {prompt.tags.map((tag, index) => (
                  <Badge key={index} variant="outline" className="text-sm">
                    {tag}
                  </Badge>
                ))}
              </div>
            </div>
          )}

          {/* Main Prompt */}
          <div>
            <div className="flex items-center justify-between mb-3">
              <h3 className="font-semibold text-gray-900 flex items-center">
                <BookOpen className="h-4 w-4 mr-2" />
                Prompt Completo
              </h3>
              <Button
                variant="outline"
                size="sm"
                onClick={() => copyToClipboard(promptText)}
                className="gap-2"
              >
                {copied ? (
                  <>
                    <Check className="h-4 w-4 text-green-600" />
                    Copiado!
                  </>
                ) : (
                  <>
                    <Copy className="h-4 w-4" />
                    Copiar Prompt
                  </>
                )}
              </Button>
            </div>
            <Card>
              <CardContent className="p-4">
                <div className="bg-gray-50 p-4 rounded-lg border-l-4 border-blue-500">
                  <pre className="whitespace-pre-wrap text-sm text-gray-800 font-mono leading-relaxed">
                    {promptText}
                  </pre>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Customization Area */}
          <div>
            <h3 className="font-semibold text-gray-900 mb-3 flex items-center">
              <Zap className="h-4 w-4 mr-2" />
              Personalize o Prompt
            </h3>
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Área de Personalização</CardTitle>
                <CardDescription>
                  Modifique o prompt abaixo para suas necessidades específicas
                </CardDescription>
              </CardHeader>
              <CardContent>
                <Textarea
                  placeholder="Cole o prompt aqui e faça suas modificações..."
                  value={customizedPrompt}
                  onChange={(e) => setCustomizedPrompt(e.target.value)}
                  className="min-h-[120px] font-mono text-sm"
                />
                <div className="flex gap-2 mt-3">
                  <Button
                    variant="outline"
                    onClick={() => setCustomizedPrompt(promptText)}
                    size="sm"
                  >
                    Usar Prompt Original
                  </Button>
                  <Button
                    variant="outline"
                    onClick={() => copyToClipboard(customizedPrompt)}
                    disabled={!customizedPrompt}
                    size="sm"
                  >
                    <Copy className="h-4 w-4 mr-1" />
                    Copiar Personalizado
                  </Button>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Usage Tips */}
          <div>
            <h3 className="font-semibold text-gray-900 mb-3">💡 Dicas de Uso</h3>
            <Card>
              <CardContent className="p-4">
                <ul className="space-y-2 text-sm text-gray-600">
                  <li className="flex items-start">
                    <span className="text-blue-500 mr-2">•</span>
                    <span>Substitua [VARIÁVEIS] pelos seus dados específicos</span>
                  </li>
                  <li className="flex items-start">
                    <span className="text-blue-500 mr-2">•</span>
                    <span>Seja específico nos detalhes para melhores resultados</span>
                  </li>
                  <li className="flex items-start">
                    <span className="text-blue-500 mr-2">•</span>
                    <span>Teste diferentes variações para encontrar a melhor versão</span>
                  </li>
                  <li className="flex items-start">
                    <span className="text-blue-500 mr-2">•</span>
                    <span>Use este prompt em ChatGPT, Claude, Gemini ou outras IAs</span>
                  </li>
                </ul>
              </CardContent>
            </Card>
          </div>

          {/* Action Buttons */}
          <div className="flex gap-3 pt-4 border-t">
            <Button 
              onClick={() => copyToClipboard(promptText)}
              className="flex-1"
            >
              <Copy className="h-4 w-4 mr-2" />
              Copiar e Usar
            </Button>
            <Button 
              variant="outline" 
              onClick={onClose}
              className="px-8"
            >
              Fechar
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};

// Generate sample prompt text based on prompt data
function generateSamplePrompt(prompt: Prompt): string {
  const templates = {
    'Marketing': `Você é um especialista em marketing digital com 10+ anos de experiência. 

Sua tarefa: ${prompt.title.toLowerCase()}

Contexto: [DESCREVA SEU NEGÓCIO/PRODUTO]
Público-alvo: [DEFINA SEU PÚBLICO]
Objetivo: [QUAL RESULTADO VOCÊ QUER]

Por favor, crie uma estratégia detalhada que inclua:
1. Análise do público-alvo
2. Mensagens-chave
3. Canais recomendados
4. Métricas de sucesso

Formato: Resposta estruturada e acionável
Tom: Profissional e prático`,

    'Produtividade': `Atue como um consultor de produtividade especializado em otimização de processos.

Objetivo: ${prompt.title.toLowerCase()}

Situação atual: [DESCREVA SUA SITUAÇÃO]
Desafios: [LISTE SEUS PRINCIPAIS DESAFIOS]
Recursos disponíveis: [TEMPO, FERRAMENTAS, EQUIPE]

Forneça:
1. Análise da situação atual
2. Plano de ação passo a passo
3. Ferramentas recomendadas
4. Cronograma de implementação

Estilo: Direto, prático e implementável`,

    'Conteúdo': `Você é um criador de conteúdo experiente e estrategista editorial.

Tarefa: ${prompt.title.toLowerCase()}

Especificações:
- Tema: [SEU TEMA ESPECÍFICO]
- Público: [DEFINA SUA AUDIÊNCIA]
- Plataforma: [ONDE SERÁ PUBLICADO]
- Objetivo: [EDUCAR/ENTRETER/CONVERTER]

Crie conteúdo que seja:
1. Envolvente e relevante
2. Otimizado para a plataforma
3. Alinhado com o objetivo
4. Acionável para o público

Inclua sugestões de título, estrutura e call-to-action.`,

    'default': `Você é um especialista em ${prompt.category.toLowerCase()} com vasta experiência prática.

Objetivo: ${prompt.title.toLowerCase()}

Contexto: [FORNEÇA DETALHES ESPECÍFICOS SOBRE SUA SITUAÇÃO]
Requisitos: [LISTE SEUS REQUISITOS ESPECÍFICOS]
Restrições: [MENCIONE LIMITAÇÕES OU RESTRIÇÕES]

Por favor, forneça uma resposta detalhada e prática que inclua:
1. Análise da situação
2. Recomendações específicas
3. Passos de implementação
4. Considerações importantes

Formato: Estruturado e fácil de seguir
Tom: Profissional e útil`
  };

  return templates[prompt.category as keyof typeof templates] || templates.default;
}

export default PromptModal;
