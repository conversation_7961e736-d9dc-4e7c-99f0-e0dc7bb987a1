import { useState } from "react";
import { Search, Calendar, User, ArrowRight, Book<PERSON>pen, TrendingUp, Clock } from "lucide-react";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Link } from "react-router-dom";
import Header from "@/components/Header";
import SEOHead from "@/components/SEOHead";

interface BlogPost {
  id: number;
  title: string;
  excerpt: string;
  content: string;
  category: string;
  author: string;
  publishDate: string;
  readTime: string;
  tags: string[];
  featured?: boolean;
  glossaryTerms: string[]; // Terms that link to glossary
}

const Blog = () => {
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedCategory, setSelectedCategory] = useState("all");

  // SEO-optimized blog posts with glossary links
  const blogPosts: BlogPost[] = [
    {
      id: 1,
      title: "Guia Completo de Prompt Engineering: Como Dominar a Arte dos Prompts de IA",
      excerpt: "Descubra as técnicas avançadas de prompt engineering para maximizar o potencial do ChatGPT, Claude e outras IAs. Aprenda a criar prompts que geram resultados excepcionais.",
      content: "O prompt engineering é uma habilidade essencial na era da inteligência artificial...",
      category: "IA e Prompts",
      author: "Prompt Genius AI",
      publishDate: "2024-01-15",
      readTime: "12 min",
      tags: ["prompt engineering", "chatgpt", "ia", "produtividade"],
      featured: true,
      glossaryTerms: ["Prompt Engineering", "ChatGPT", "Inteligência Artificial (IA)", "Prompt"]
    },
    {
      id: 2,
      title: "As 50 Melhores Ferramentas de IA para Produtividade em 2024",
      excerpt: "Lista completa das ferramentas de inteligência artificial que estão revolucionando a produtividade. De ChatGPT a Midjourney, descubra as melhores opções.",
      content: "A revolução da IA trouxe ferramentas incríveis para aumentar nossa produtividade...",
      category: "Ferramentas IA",
      author: "Prompt Genius AI",
      publishDate: "2024-01-10",
      readTime: "15 min",
      tags: ["ferramentas ia", "produtividade", "chatgpt", "midjourney"],
      featured: true,
      glossaryTerms: ["ChatGPT", "Midjourney", "DALL-E", "Claude", "Jasper AI"]
    },
    {
      id: 3,
      title: "No-Code Revolution: Como Criar Aplicativos Sem Programar",
      excerpt: "Explore o mundo do desenvolvimento no-code com Bubble, Webflow e Zapier. Aprenda a criar aplicações profissionais sem escrever uma linha de código.",
      content: "O movimento no-code está democratizando o desenvolvimento de software...",
      category: "No-Code",
      author: "Prompt Genius AI",
      publishDate: "2024-01-08",
      readTime: "10 min",
      tags: ["no-code", "bubble", "webflow", "zapier"],
      glossaryTerms: ["No-Code", "Bubble", "Webflow", "Zapier", "Automação"]
    },
    {
      id: 4,
      title: "SEO com IA: Como Otimizar Conteúdo Usando Inteligência Artificial",
      excerpt: "Descubra como usar ferramentas de IA para pesquisa de palavras-chave, criação de conteúdo otimizado e melhoria do ranking no Google.",
      content: "A inteligência artificial está transformando o SEO de maneiras revolucionárias...",
      category: "SEO e Marketing",
      author: "Prompt Genius AI",
      publishDate: "2024-01-05",
      readTime: "14 min",
      tags: ["seo", "marketing digital", "ia", "google"],
      glossaryTerms: ["SEO", "Marketing Digital", "Copywriting", "KPI (Key Performance Indicator)"]
    },
    {
      id: 5,
      title: "Machine Learning vs Deep Learning: Entenda as Diferenças",
      excerpt: "Guia completo sobre as diferenças entre Machine Learning e Deep Learning, com exemplos práticos e aplicações no mundo real.",
      content: "Machine Learning e Deep Learning são termos frequentemente confundidos...",
      category: "IA e Tecnologia",
      author: "Prompt Genius AI",
      publishDate: "2024-01-03",
      readTime: "11 min",
      tags: ["machine learning", "deep learning", "ia", "tecnologia"],
      glossaryTerms: ["Machine Learning", "Deep Learning", "LLM (Large Language Model)", "Transformer"]
    },
    {
      id: 6,
      title: "Copywriting com IA: Técnicas Avançadas para Textos que Convertem",
      excerpt: "Aprenda a usar Jasper AI, Copy.ai e ChatGPT para criar textos persuasivos que aumentam suas vendas e engajamento.",
      content: "O copywriting assistido por IA está revolucionando a criação de conteúdo...",
      category: "Marketing Digital",
      author: "Prompt Genius AI",
      publishDate: "2024-01-01",
      readTime: "13 min",
      tags: ["copywriting", "vendas", "marketing", "conversão"],
      glossaryTerms: ["Copywriting", "Jasper AI", "Copy.ai", "ChatGPT"]
    }
  ];

  const categories = ["IA e Prompts", "Ferramentas IA", "No-Code", "SEO e Marketing", "IA e Tecnologia", "Marketing Digital"];

  const filteredPosts = blogPosts.filter(post => {
    const matchesSearch = post.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         post.excerpt.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         post.tags.some(tag => tag.toLowerCase().includes(searchTerm.toLowerCase()));
    
    const matchesCategory = selectedCategory === "all" || post.category === selectedCategory;
    
    return matchesSearch && matchesCategory;
  });

  const featuredPosts = blogPosts.filter(post => post.featured);

  return (
    <div className="min-h-screen bg-gradient-to-br from-emerald-50 via-white to-teal-50">
      <SEOHead
        title="Blog IA - Artigos sobre Inteligência Artificial | Prompt Genius AI"
        description="Artigos especializados sobre IA, Prompt Engineering, No-Code e tecnologia. Guias práticos para dominar ChatGPT, ferramentas de IA e automação."
        keywords={[
          "blog ia", "artigos inteligência artificial", "prompt engineering guia", "chatgpt tutorial",
          "no-code tutorial", "ferramentas ia 2024", "automação ia", "copywriting ia",
          "seo inteligência artificial", "marketing digital ia", "machine learning", "deep learning"
        ]}
      />
      <Header />
      
      {/* Hero Section */}
      <section className="py-16 px-4">
        <div className="max-w-7xl mx-auto text-center">
          <div className="mb-6">
            <BookOpen className="h-16 w-16 mx-auto text-emerald-600 mb-4" />
          </div>
          <h1 className="text-4xl md:text-6xl font-bold text-gray-900 mb-6">
            Blog Prompt Genius AI
            <span className="block text-transparent bg-clip-text bg-gradient-to-r from-emerald-600 to-teal-600">
              Conteúdo Especializado em IA
            </span>
          </h1>
          <p className="text-xl text-gray-600 mb-8 max-w-3xl mx-auto">
            Artigos aprofundados sobre Inteligência Artificial, Prompt Engineering, No-Code e 
            as últimas tendências em tecnologia. Conteúdo criado por especialistas.
          </p>
        </div>
      </section>

      {/* Search and Filter */}
      <section className="py-8 px-4">
        <div className="max-w-4xl mx-auto">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="md:col-span-2 relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-5 w-5" />
              <Input
                placeholder="Buscar artigos..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10 h-12"
              />
            </div>
            <select
              value={selectedCategory}
              onChange={(e) => setSelectedCategory(e.target.value)}
              className="h-12 px-3 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-emerald-500"
            >
              <option value="all">Todas as Categorias</option>
              {categories.map(category => (
                <option key={category} value={category}>{category}</option>
              ))}
            </select>
          </div>
        </div>
      </section>

      {/* Featured Posts */}
      {!searchTerm && featuredPosts.length > 0 && (
        <section className="py-8 px-4">
          <div className="max-w-7xl mx-auto">
            <div className="flex items-center mb-6">
              <TrendingUp className="h-6 w-6 text-emerald-600 mr-2" />
              <h2 className="text-2xl font-bold text-gray-900">Artigos em Destaque</h2>
            </div>
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
              {featuredPosts.slice(0, 2).map((post) => (
                <Card key={post.id} className="hover:shadow-lg transition-shadow cursor-pointer group border-emerald-200">
                  <CardHeader>
                    <div className="flex justify-between items-start mb-2">
                      <Badge variant="secondary" className="text-xs bg-emerald-100 text-emerald-800">
                        {post.category}
                      </Badge>
                      <div className="flex items-center text-sm text-gray-500">
                        <Clock className="h-4 w-4 mr-1" />
                        {post.readTime}
                      </div>
                    </div>
                    <CardTitle className="text-xl group-hover:text-emerald-600 transition-colors">
                      {post.title}
                    </CardTitle>
                    <CardDescription className="text-base">
                      {post.excerpt}
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="flex items-center justify-between text-sm text-gray-500 mb-4">
                      <div className="flex items-center space-x-1">
                        <User className="h-4 w-4" />
                        <span>{post.author}</span>
                      </div>
                      <div className="flex items-center space-x-1">
                        <Calendar className="h-4 w-4" />
                        <span>{new Date(post.publishDate).toLocaleDateString('pt-BR')}</span>
                      </div>
                    </div>
                    
                    <div className="flex flex-wrap gap-1 mb-4">
                      {post.tags.slice(0, 4).map((tag, index) => (
                        <Badge key={index} variant="outline" className="text-xs">
                          {tag}
                        </Badge>
                      ))}
                    </div>

                    <div className="mb-4">
                      <p className="text-sm text-gray-600 mb-2">
                        <strong>Termos do Glossário:</strong>
                      </p>
                      <div className="flex flex-wrap gap-1">
                        {post.glossaryTerms.slice(0, 3).map((term, index) => (
                          <Link key={index} to="/glossario">
                            <Badge variant="outline" className="text-xs hover:bg-emerald-50 cursor-pointer">
                              {term}
                            </Badge>
                          </Link>
                        ))}
                        {post.glossaryTerms.length > 3 && (
                          <Link to="/glossario">
                            <Badge variant="outline" className="text-xs hover:bg-emerald-50 cursor-pointer">
                              +{post.glossaryTerms.length - 3} termos
                            </Badge>
                          </Link>
                        )}
                      </div>
                    </div>

                    <Button className="w-full group-hover:bg-emerald-600 transition-colors">
                      Ler Artigo Completo
                      <ArrowRight className="ml-2 h-4 w-4" />
                    </Button>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        </section>
      )}

      {/* All Posts */}
      <section className="py-8 px-4">
        <div className="max-w-7xl mx-auto">
          <div className="flex justify-between items-center mb-6">
            <h2 className="text-2xl font-bold text-gray-900">
              {searchTerm ? `${filteredPosts.length} artigos encontrados` : 'Todos os Artigos'}
            </h2>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {filteredPosts.map((post) => (
              <Card key={post.id} className="hover:shadow-lg transition-shadow cursor-pointer group">
                <CardHeader>
                  <div className="flex justify-between items-start mb-2">
                    <Badge variant="secondary" className="text-xs">
                      {post.category}
                    </Badge>
                    <div className="flex items-center text-sm text-gray-500">
                      <Clock className="h-4 w-4 mr-1" />
                      {post.readTime}
                    </div>
                  </div>
                  <CardTitle className="text-lg group-hover:text-emerald-600 transition-colors">
                    {post.title}
                  </CardTitle>
                  <CardDescription className="text-sm">
                    {post.excerpt}
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="flex items-center justify-between text-sm text-gray-500 mb-4">
                    <div className="flex items-center space-x-1">
                      <User className="h-4 w-4" />
                      <span>{post.author}</span>
                    </div>
                    <div className="flex items-center space-x-1">
                      <Calendar className="h-4 w-4" />
                      <span>{new Date(post.publishDate).toLocaleDateString('pt-BR')}</span>
                    </div>
                  </div>
                  
                  <div className="flex flex-wrap gap-1 mb-4">
                    {post.tags.slice(0, 3).map((tag, index) => (
                      <Badge key={index} variant="outline" className="text-xs">
                        {tag}
                      </Badge>
                    ))}
                  </div>

                  <div className="mb-4">
                    <div className="flex flex-wrap gap-1">
                      {post.glossaryTerms.slice(0, 2).map((term, index) => (
                        <Link key={index} to="/glossario">
                          <Badge variant="outline" className="text-xs hover:bg-emerald-50 cursor-pointer">
                            📖 {term}
                          </Badge>
                        </Link>
                      ))}
                    </div>
                  </div>

                  <Button className="w-full group-hover:bg-emerald-600 transition-colors">
                    Ler Artigo
                    <ArrowRight className="ml-2 h-4 w-4" />
                  </Button>
                </CardContent>
              </Card>
            ))}
          </div>

          {filteredPosts.length === 0 && (
            <div className="text-center py-12">
              <div className="text-gray-400 mb-4">
                <Search className="h-16 w-16 mx-auto" />
              </div>
              <h3 className="text-xl font-semibold text-gray-900 mb-2">
                Nenhum artigo encontrado
              </h3>
              <p className="text-gray-600">
                Tente ajustar seu termo de busca ou categoria
              </p>
            </div>
          )}
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-16 px-4 bg-emerald-50">
        <div className="max-w-4xl mx-auto text-center">
          <h3 className="text-3xl font-bold text-gray-900 mb-4">
            Quer Dominar a IA?
          </h3>
          <p className="text-xl text-gray-600 mb-8">
            Explore nosso glossário completo com mais de 45 termos essenciais sobre IA e tecnologia.
          </p>
          <Link to="/glossario">
            <Button size="lg" className="bg-emerald-600 hover:bg-emerald-700">
              Ver Glossário Completo
              <BookOpen className="ml-2 h-5 w-5" />
            </Button>
          </Link>
        </div>
      </section>
    </div>
  );
};

export default Blog;
