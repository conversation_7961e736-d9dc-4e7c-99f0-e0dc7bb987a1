# Writing an Upsell Page

### FILL-IN-THE-BLANK PROMPTS**:**

```jsx
Write a **[word count]** word sales page that starts by saying "Wait! Your order is not complete." And then uses persuasive language to create a sense of urgency around buying a product named **[name of the product]**, which has the following benefits:
1. **[Benefit 1]**
2. **[Benefit 2]**
3. **[Benefit 3]**
Mention that the reader will be able to **[promise]**, or they will **[guarantee].**
```

```jsx
Write a 300 word sales page that introduces **[your product]** and describes these key benefits **[benefit 1]**, **[benefit 2]**, **[benefit 3]**. Showcase how this product will help the audience, which are **[niche]**, to achieve **[end result]**. Address these pain points the target audience is facing **[pain point 1]**, **[pain point 2]**, **[pain point 3]**, and explain how this product can help solve them.

Use these testimonials to build credibility and demonstrate the effectiveness of the product:
**[Testimonial 1]
[Testimonial 2]
[Testimonial 3]**

Create a sense of urgency by highlighting that **[urgency]** and **[scarcity]**

Use clear, concise language and formatting to make your sales page easy to read and navigate.
Close by urging potential customers to take action and make a purchase, while reinforcing the key benefits and value of the product.
```

### OPEN-ENDED PROMPTS**:**

1. “I am selling [describe upsell]. Can you write a persuasive, 200-word text offering the product to customers who just bought [describe main offer]?”
2. “Can you write an upsell page that convinces customers to upgrade to our [describe product]?”
3. “I need an upsell page that highlights the benefits of [describe product]. Can you help me with that?”
4. “I want to create an upsell page that showcases the features of our VIP membership. Can you assist me with that?”
5. “Can you help me write an upsell page that promotes our advanced version of the software?”
6. “I need an upsell page that persuades customers to upgrade to our platinum plan. Can you write that for me?”
7. “Can you create an upsell page that highlights the additional resources included in our premium package? We offer [describe your business]”
8. “I want to offer an upsell to customers who purchase our basic service. Can you help me write the page that makes the offer? We sell [product] to [niche].”
9. “Can you write an upsell page that showcases the benefits of our comprehensive training program?”
10. “I need an upsell page that convinces customers to upgrade to our higher-tier product. Can you assist me with that? [Introduce details about your product and audience]”

### EXAMPLES:

![Captura de pantalla 2023-02-25 a la(s) 7.28.20 a.m..png](Captura_de_pantalla_2023-02-25_a_la(s)_7.28.20_a.m..png)

![Captura de pantalla 2023-02-25 a la(s) 7.28.25 a.m..png](Captura_de_pantalla_2023-02-25_a_la(s)_7.28.25_a.m..png)

![Captura de pantalla 2023-02-25 a la(s) 7.29.00 a.m..png](Captura_de_pantalla_2023-02-25_a_la(s)_7.29.00_a.m..png)