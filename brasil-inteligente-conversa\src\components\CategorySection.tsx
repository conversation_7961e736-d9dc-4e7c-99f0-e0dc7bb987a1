
import { <PERSON><PERSON> } from "@/components/ui/button";
import { ArrowRight } from "lucide-react";
import { Link } from "react-router-dom";
import ResourceCard from "./ResourceCard";
import type { Prompt } from "@/data/prompts";

interface CategorySectionProps {
  title: string;
  description: string;
  icon: React.ReactNode;
  items: any[];
  type: 'prompt' | 'tool' | 'nocode';
  onPromptClick?: (prompt: Prompt) => void;
}

const CategorySection = ({ title, description, icon, items, type, onPromptClick }: CategorySectionProps) => {
  const getNavigationPath = () => {
    switch (type) {
      case 'prompt': return '/prompts';
      case 'tool': return '/ferramentas-ia';
      case 'nocode': return '/no-code';
      default: return '/';
    }
  };

  return (
    <section className="mb-16">
      <div className="flex items-center justify-between mb-8">
        <div className="flex items-center space-x-4">
          {icon}
          <div>
            <h2 className="text-3xl font-bold text-gray-900">{title}</h2>
            <p className="text-gray-600 mt-1">{description}</p>
          </div>
        </div>
        <Link to={getNavigationPath()}>
          <Button variant="outline" className="hidden md:flex items-center">
            Ver Todos
            <ArrowRight className="ml-2 h-4 w-4" />
          </Button>
        </Link>
      </div>
      
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
        {items.slice(0, 8).map((item) => (
          <ResourceCard key={item.id} item={item} type={type} onPromptClick={onPromptClick} />
        ))}
      </div>
      
      {items.length > 8 && (
        <div className="text-center mt-8">
          <Link to={getNavigationPath()}>
            <Button className="bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700">
              Ver Mais {type === 'prompt' ? 'Prompts' : type === 'tool' ? 'Ferramentas IA' : 'Ferramentas No-Code'}
              <ArrowRight className="ml-2 h-4 w-4" />
            </Button>
          </Link>
        </div>
      )}
    </section>
  );
};

export default CategorySection;
