# Content Template for Adding Notion Data

This file shows you how to add content from your Notion page to the project.

## How to Add Prompts

Add new prompts to `src/data/prompts.ts` in this format:

```typescript
{
  id: 41, // Next available ID
  title: "Your Prompt Title",
  description: "Brief description of what this prompt does",
  category: "Category Name", // Use existing categories or add new ones
  rating: 4.8, // Rating from 1-5
  uses: 1500, // Number of uses/popularity
  tags: ["tag1", "tag2", "tag3"], // Relevant tags
  difficulty: "Iniciante" | "Intermediário" | "Avançado",
  url: "https://link-to-prompt.com", // Optional
  prompt: "The actual prompt text" // Optional
}
```

### Available Prompt Categories:
- Marketing
- Produtividade  
- Conteúdo
- Empreendedorismo
- Educação
- Vendas
- Design
- Programação
- Análise de Dados
- Recursos Humanos
- Finanças
- Jurídico
- Saúde
- E-commerce
- Redes Sociais
- SEO
- Copywriting
- Tradução
- Pesquisa
- Criatividade

## How to Add AI Tools

Add new AI tools to `src/data/aiTools.ts` in this format:

```typescript
{
  id: 41, // Next available ID
  title: "Tool Name",
  description: "What this tool does",
  category: "Category Name",
  rating: 4.7,
  uses: 2000,
  type: "Gratuito" | "Pago" | "Gratuito/Pago" | "Freemium",
  url: "https://tool-website.com",
  tags: ["tag1", "tag2"],
  pricing: "Pricing information",
  features: ["feature1", "feature2"] // Optional
}
```

### Available AI Tool Categories:
- Conversacional
- Design
- Produtividade
- Pesquisa
- Escrita
- Código
- Vídeo
- Áudio
- Imagem
- Análise
- Marketing
- Vendas
- Educação
- Tradução
- Apresentações
- Dados
- Automação
- SEO
- Social Media
- E-commerce

## How to Add No-Code Tools

Add new no-code tools to `src/data/noCodeTools.ts` in this format:

```typescript
{
  id: 41, // Next available ID
  title: "Tool Name",
  description: "What this tool does",
  category: "Category Name",
  rating: 4.5,
  uses: 1800,
  type: "Gratuito" | "Pago" | "Gratuito/Pago" | "Freemium",
  url: "https://tool-website.com",
  tags: ["tag1", "tag2"],
  pricing: "Pricing information",
  difficulty: "Iniciante" | "Intermediário" | "Avançado",
  features: ["feature1", "feature2"] // Optional
}
```

### Available No-Code Tool Categories:
- Desenvolvimento
- Automação
- Web Design
- Produtividade
- E-commerce
- Marketing
- Banco de Dados
- Formulários
- Landing Pages
- Apps Mobile
- Chatbots
- Workflows
- Analytics
- CRM
- Email Marketing
- Pagamentos
- Integração
- Prototipagem
- Colaboração
- Documentação

## Steps to Add Content from Notion:

1. **Copy content from your Notion page**
2. **Organize by type** (Prompts, AI Tools, No-Code Tools)
3. **Format according to the templates above**
4. **Add to the respective data files**
5. **Update the ID numbers** to be sequential
6. **Test the application** to make sure everything works

## Example of Adding Multiple Items:

```typescript
// Add these to the end of the respective arrays in the data files

// In prompts.ts
{
  id: 41,
  title: "Gerador de Meta Descriptions",
  description: "Crie meta descriptions otimizadas para SEO",
  category: "SEO",
  rating: 4.6,
  uses: 1200,
  tags: ["meta description", "SEO", "otimização"],
  difficulty: "Iniciante"
},
{
  id: 42,
  title: "Criador de Funis de Vendas",
  description: "Desenvolva funis de vendas completos e eficazes",
  category: "Vendas",
  rating: 4.8,
  uses: 1800,
  tags: ["funil", "vendas", "conversão"],
  difficulty: "Avançado"
}

// In aiTools.ts
{
  id: 41,
  title: "Loom AI",
  description: "Gravação de tela com recursos de IA",
  category: "Vídeo",
  rating: 4.5,
  uses: 2200,
  type: "Freemium",
  url: "https://loom.com",
  tags: ["gravação", "tela", "vídeo"],
  pricing: "Gratuito com limitações, Business $8/mês"
}
```

## Current Statistics:
- **Prompts**: 40 items
- **AI Tools**: 40 items  
- **No-Code Tools**: 40 items
- **Total**: 120 items

The application will automatically update the counts and search functionality when you add new items.
