
import { <PERSON>, Users, ArrowRight, ExternalLink } from "lucide-react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import type { Prompt } from "@/data/prompts";

interface ResourceCardProps {
  item: {
    id: number;
    title: string;
    description: string;
    category: string;
    rating: number;
    uses: number;
    type?: string;
  };
  type: 'prompt' | 'tool' | 'nocode';
  onPromptClick?: (prompt: Prompt) => void;
}

const ResourceCard = ({ item, type, onPromptClick }: ResourceCardProps) => {
  const getTypeColor = () => {
    switch (type) {
      case 'prompt':
        return 'bg-blue-100 text-blue-800';
      case 'tool':
        return 'bg-purple-100 text-purple-800';
      case 'nocode':
        return 'bg-green-100 text-green-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getTypeLabel = () => {
    switch (type) {
      case 'prompt':
        return 'Prompt';
      case 'tool':
        return 'IA Tool';
      case 'nocode':
        return 'No-Code';
      default:
        return 'Recurso';
    }
  };

  const handleClick = () => {
    if (type === 'prompt' && onPromptClick) {
      onPromptClick(item as Prompt);
    }
  };

  return (
    <Card
      className="group hover:shadow-lg transition-all duration-300 hover:-translate-y-1 bg-white/80 backdrop-blur-sm border border-gray-200 cursor-pointer"
      onClick={handleClick}
    >
      <CardHeader className="pb-3">
        <div className="flex items-start justify-between mb-2">
          <Badge className={getTypeColor()}>
            {getTypeLabel()}
          </Badge>
          <div className="flex items-center text-sm text-gray-500">
            <Star className="h-4 w-4 fill-yellow-400 text-yellow-400 mr-1" />
            {item.rating}
          </div>
        </div>
        <CardTitle className="text-lg group-hover:text-blue-600 transition-colors">
          {item.title}
        </CardTitle>
        <CardDescription className="text-gray-600">
          {item.description}
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4 text-sm text-gray-500">
            <span className="flex items-center">
              <Users className="h-4 w-4 mr-1" />
              {item.uses.toLocaleString('pt-BR')} usos
            </span>
            {item.type && (
              <Badge variant="outline" className="text-xs">
                {item.type}
              </Badge>
            )}
          </div>
          <Button
            size="sm"
            variant="ghost"
            className="group-hover:bg-blue-50 group-hover:text-blue-600"
            onClick={(e) => {
              e.stopPropagation();
              handleClick();
            }}
          >
            {type === 'prompt' ? 'Ver Prompt' : 'Ver'}
            <ArrowRight className="h-4 w-4 ml-1 group-hover:translate-x-1 transition-transform" />
          </Button>
        </div>
      </CardContent>
    </Card>
  );
};

export default ResourceCard;
