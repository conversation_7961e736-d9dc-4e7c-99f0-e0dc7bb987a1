// Export all data types and collections
export { prompts, promptCategories, type Prompt } from './prompts';
export { aiTools, aiToolCategories, type AITool } from './aiTools';
export { noCodeTools, noCodeCategories, type NoCodeTool } from './noCodeTools';

// Combined search functionality
import { prompts, type Prompt } from './prompts';
import { aiTools, type AITool } from './aiTools';
import { noCodeTools, type NoCodeTool } from './noCodeTools';

export interface SearchResult {
  id: number;
  title: string;
  description: string;
  category: string;
  rating: number;
  uses: number;
  type: 'prompt' | 'aiTool' | 'noCodeTool';
  originalItem: Prompt | AITool | NoCodeTool;
}

export const searchAll = (searchTerm: string): SearchResult[] => {
  const term = searchTerm.toLowerCase();
  const results: SearchResult[] = [];

  // Search prompts
  prompts.forEach(prompt => {
    if (
      prompt.title.toLowerCase().includes(term) ||
      prompt.description.toLowerCase().includes(term) ||
      prompt.category.toLowerCase().includes(term) ||
      prompt.tags?.some(tag => tag.toLowerCase().includes(term))
    ) {
      results.push({
        id: prompt.id,
        title: prompt.title,
        description: prompt.description,
        category: prompt.category,
        rating: prompt.rating,
        uses: prompt.uses,
        type: 'prompt',
        originalItem: prompt
      });
    }
  });

  // Search AI tools
  aiTools.forEach(tool => {
    if (
      tool.title.toLowerCase().includes(term) ||
      tool.description.toLowerCase().includes(term) ||
      tool.category.toLowerCase().includes(term) ||
      tool.tags?.some(tag => tag.toLowerCase().includes(term))
    ) {
      results.push({
        id: tool.id + 10000, // Offset to avoid ID conflicts
        title: tool.title,
        description: tool.description,
        category: tool.category,
        rating: tool.rating,
        uses: tool.uses,
        type: 'aiTool',
        originalItem: tool
      });
    }
  });

  // Search no-code tools
  noCodeTools.forEach(tool => {
    if (
      tool.title.toLowerCase().includes(term) ||
      tool.description.toLowerCase().includes(term) ||
      tool.category.toLowerCase().includes(term) ||
      tool.tags?.some(tag => tag.toLowerCase().includes(term))
    ) {
      results.push({
        id: tool.id + 20000, // Offset to avoid ID conflicts
        title: tool.title,
        description: tool.description,
        category: tool.category,
        rating: tool.rating,
        uses: tool.uses,
        type: 'noCodeTool',
        originalItem: tool
      });
    }
  });

  // Sort by relevance (rating * uses)
  return results.sort((a, b) => (b.rating * b.uses) - (a.rating * a.uses));
};

export const getPopularItems = (limit: number = 10): SearchResult[] => {
  const allItems: SearchResult[] = [];

  // Add all prompts
  prompts.forEach(prompt => {
    allItems.push({
      id: prompt.id,
      title: prompt.title,
      description: prompt.description,
      category: prompt.category,
      rating: prompt.rating,
      uses: prompt.uses,
      type: 'prompt',
      originalItem: prompt
    });
  });

  // Add all AI tools
  aiTools.forEach(tool => {
    allItems.push({
      id: tool.id + 10000,
      title: tool.title,
      description: tool.description,
      category: tool.category,
      rating: tool.rating,
      uses: tool.uses,
      type: 'aiTool',
      originalItem: tool
    });
  });

  // Add all no-code tools
  noCodeTools.forEach(tool => {
    allItems.push({
      id: tool.id + 20000,
      title: tool.title,
      description: tool.description,
      category: tool.category,
      rating: tool.rating,
      uses: tool.uses,
      type: 'noCodeTool',
      originalItem: tool
    });
  });

  // Sort by popularity (rating * uses) and return top items
  return allItems
    .sort((a, b) => (b.rating * b.uses) - (a.rating * a.uses))
    .slice(0, limit);
};

export const getItemsByCategory = (category: string, type?: 'prompt' | 'aiTool' | 'noCodeTool'): SearchResult[] => {
  const results: SearchResult[] = [];

  if (!type || type === 'prompt') {
    prompts
      .filter(prompt => prompt.category.toLowerCase() === category.toLowerCase())
      .forEach(prompt => {
        results.push({
          id: prompt.id,
          title: prompt.title,
          description: prompt.description,
          category: prompt.category,
          rating: prompt.rating,
          uses: prompt.uses,
          type: 'prompt',
          originalItem: prompt
        });
      });
  }

  if (!type || type === 'aiTool') {
    aiTools
      .filter(tool => tool.category.toLowerCase() === category.toLowerCase())
      .forEach(tool => {
        results.push({
          id: tool.id + 10000,
          title: tool.title,
          description: tool.description,
          category: tool.category,
          rating: tool.rating,
          uses: tool.uses,
          type: 'aiTool',
          originalItem: tool
        });
      });
  }

  if (!type || type === 'noCodeTool') {
    noCodeTools
      .filter(tool => tool.category.toLowerCase() === category.toLowerCase())
      .forEach(tool => {
        results.push({
          id: tool.id + 20000,
          title: tool.title,
          description: tool.description,
          category: tool.category,
          rating: tool.rating,
          uses: tool.uses,
          type: 'noCodeTool',
          originalItem: tool
        });
      });
  }

  return results.sort((a, b) => (b.rating * b.uses) - (a.rating * a.uses));
};

export const getStats = () => {
  return {
    totalPrompts: prompts.length,
    totalAITools: aiTools.length,
    totalNoCodeTools: noCodeTools.length,
    totalItems: prompts.length + aiTools.length + noCodeTools.length,
    totalUses: prompts.reduce((sum, p) => sum + p.uses, 0) + 
               aiTools.reduce((sum, t) => sum + t.uses, 0) + 
               noCodeTools.reduce((sum, t) => sum + t.uses, 0),
    averageRating: {
      prompts: prompts.reduce((sum, p) => sum + p.rating, 0) / prompts.length,
      aiTools: aiTools.reduce((sum, t) => sum + t.rating, 0) / aiTools.length,
      noCodeTools: noCodeTools.reduce((sum, t) => sum + t.rating, 0) / noCodeTools.length
    }
  };
};
