
import { useState } from "react";
import { <PERSON>u, <PERSON>, <PERSON>, <PERSON>, BookOpen } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";

const Header = () => {
  const [isMenuOpen, setIsMenuOpen] = useState(false);

  return (
    <header className="bg-white/90 backdrop-blur-md border-b border-gray-200 sticky top-0 z-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between items-center h-16">
          {/* Logo */}
          <div className="flex items-center space-x-2">
            <div className="w-10 h-10 bg-gradient-to-br from-blue-600 to-purple-600 rounded-lg flex items-center justify-center">
              <Star className="h-6 w-6 text-white" />
            </div>
            <div>
              <h1 className="text-xl font-bold text-gray-900">Prompt Genius AI</h1>
              <p className="text-xs text-gray-500">Guia Total de IA</p>
            </div>
          </div>

          {/* Desktop Navigation */}
          <nav className="hidden md:flex items-center space-x-8">
            <a href="#prompts" className="text-gray-700 hover:text-blue-600 font-medium transition-colors">
              Prompts
            </a>
            <a href="#ferramentas-ia" className="text-gray-700 hover:text-blue-600 font-medium transition-colors">
              Ferramentas IA
            </a>
            <a href="#no-code" className="text-gray-700 hover:text-blue-600 font-medium transition-colors">
              No-Code
            </a>
            <a href="#guias" className="text-gray-700 hover:text-blue-600 font-medium transition-colors">
              Guias
            </a>
          </nav>

          {/* Desktop CTA */}
          <div className="hidden md:flex items-center space-x-4">
            <Button variant="outline" className="border-blue-600 text-blue-600 hover:bg-blue-50">
              Entrar
            </Button>
            <Button className="bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700">
              Começar Grátis
            </Button>
          </div>

          {/* Mobile menu button */}
          <div className="md:hidden">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setIsMenuOpen(!isMenuOpen)}
            >
              {isMenuOpen ? <X className="h-6 w-6" /> : <Menu className="h-6 w-6" />}
            </Button>
          </div>
        </div>
      </div>

      {/* Mobile Navigation */}
      {isMenuOpen && (
        <div className="md:hidden bg-white border-t border-gray-200">
          <div className="px-4 py-4 space-y-4">
            <a href="#prompts" className="block text-gray-700 hover:text-blue-600 font-medium">
              Prompts
            </a>
            <a href="#ferramentas-ia" className="block text-gray-700 hover:text-blue-600 font-medium">
              Ferramentas IA
            </a>
            <a href="#no-code" className="block text-gray-700 hover:text-blue-600 font-medium">
              No-Code
            </a>
            <a href="#guias" className="block text-gray-700 hover:text-blue-600 font-medium">
              Guias
            </a>
            <div className="pt-4 space-y-2">
              <Button variant="outline" className="w-full border-blue-600 text-blue-600 hover:bg-blue-50">
                Entrar
              </Button>
              <Button className="w-full bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700">
                Começar Grátis
              </Button>
            </div>
          </div>
        </div>
      )}
    </header>
  );
};

export default Header;
