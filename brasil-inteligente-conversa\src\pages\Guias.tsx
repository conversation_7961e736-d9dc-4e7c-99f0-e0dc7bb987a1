import { useState } from "react";
import { Search, BookOpen, Clock, User, ArrowRight, Star, TrendingUp } from "lucide-react";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import Header from "@/components/Header";

interface Guide {
  id: number;
  title: string;
  description: string;
  category: string;
  readTime: string;
  difficulty: 'Iniciante' | 'Intermediário' | 'Avançado';
  author: string;
  rating: number;
  views: number;
  tags: string[];
  featured?: boolean;
}

const Guias = () => {
  const [searchTerm, setSearchTerm] = useState("");

  // Sample guides data - in production this would come from your data source
  const guides: Guide[] = [
    {
      id: 1,
      title: "Guia Completo: Como Criar Prompts Eficazes para ChatGPT",
      description: "Aprenda as técnicas avançadas para criar prompts que geram resultados excepcionais com ChatGPT e outras IAs.",
      category: "Prompts",
      readTime: "15 min",
      difficulty: "Iniciante",
      author: "Prompt Genius AI",
      rating: 4.9,
      views: 12500,
      tags: ["chatgpt", "prompts", "ia", "iniciante"],
      featured: true
    },
    {
      id: 2,
      title: "Marketing Digital com IA: Estratégias Práticas para 2024",
      description: "Descubra como usar ferramentas de IA para revolucionar suas campanhas de marketing digital.",
      category: "Marketing",
      readTime: "20 min",
      difficulty: "Intermediário",
      author: "Prompt Genius AI",
      rating: 4.8,
      views: 9800,
      tags: ["marketing", "ia", "estratégia", "2024"]
    },
    {
      id: 3,
      title: "Automação No-Code: Do Zero ao Profissional",
      description: "Guia passo a passo para criar automações poderosas sem escrever uma linha de código.",
      category: "No-Code",
      readTime: "25 min",
      difficulty: "Intermediário",
      author: "Prompt Genius AI",
      rating: 4.7,
      views: 8200,
      tags: ["no-code", "automação", "zapier", "bubble"]
    },
    {
      id: 4,
      title: "SEO com IA: Como Otimizar Conteúdo Usando Ferramentas Inteligentes",
      description: "Aprenda a usar IA para pesquisa de palavras-chave, criação de conteúdo e otimização SEO.",
      category: "SEO",
      readTime: "18 min",
      difficulty: "Avançado",
      author: "Prompt Genius AI",
      rating: 4.9,
      views: 7500,
      tags: ["seo", "ia", "conteúdo", "otimização"]
    },
    {
      id: 5,
      title: "Produtividade com IA: 50 Ferramentas Essenciais",
      description: "Lista completa das melhores ferramentas de IA para aumentar sua produtividade no trabalho.",
      category: "Produtividade",
      readTime: "12 min",
      difficulty: "Iniciante",
      author: "Prompt Genius AI",
      rating: 4.6,
      views: 11200,
      tags: ["produtividade", "ferramentas", "ia", "trabalho"]
    },
    {
      id: 6,
      title: "Copywriting com IA: Técnicas Avançadas de Persuasão",
      description: "Domine a arte do copywriting usando IA para criar textos que convertem.",
      category: "Copywriting",
      readTime: "22 min",
      difficulty: "Avançado",
      author: "Prompt Genius AI",
      rating: 4.8,
      views: 6800,
      tags: ["copywriting", "persuasão", "vendas", "ia"]
    }
  ];

  const filteredGuides = guides.filter(guide =>
    guide.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
    guide.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
    guide.tags.some(tag => tag.toLowerCase().includes(searchTerm.toLowerCase()))
  );

  const featuredGuides = guides.filter(guide => guide.featured);

  const getDifficultyColor = (difficulty: string) => {
    switch (difficulty) {
      case 'Iniciante': return 'bg-green-100 text-green-800';
      case 'Intermediário': return 'bg-yellow-100 text-yellow-800';
      case 'Avançado': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-orange-50 via-white to-red-50">
      <Header />
      
      {/* Hero Section */}
      <section className="py-16 px-4">
        <div className="max-w-7xl mx-auto text-center">
          <div className="mb-6">
            <BookOpen className="h-16 w-16 mx-auto text-orange-600 mb-4" />
          </div>
          <h1 className="text-4xl md:text-6xl font-bold text-gray-900 mb-6">
            Guias Completos de IA
            <span className="block text-transparent bg-clip-text bg-gradient-to-r from-orange-600 to-red-600">
              Do Básico ao Avançado
            </span>
          </h1>
          <p className="text-xl text-gray-600 mb-8 max-w-3xl mx-auto">
            Aprenda a dominar ferramentas de IA, criar prompts eficazes e implementar 
            soluções no-code com nossos guias práticos e detalhados.
          </p>
        </div>
      </section>

      {/* Search */}
      <section className="py-8 px-4">
        <div className="max-w-4xl mx-auto">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-5 w-5" />
            <Input
              placeholder="Buscar guias..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10 h-12 text-lg"
            />
          </div>
        </div>
      </section>

      {/* Featured Guides */}
      {!searchTerm && featuredGuides.length > 0 && (
        <section className="py-8 px-4">
          <div className="max-w-7xl mx-auto">
            <div className="flex items-center mb-6">
              <Star className="h-6 w-6 text-yellow-500 mr-2" />
              <h2 className="text-2xl font-bold text-gray-900">Guias em Destaque</h2>
            </div>
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {featuredGuides.map((guide) => (
                <Card key={guide.id} className="hover:shadow-lg transition-shadow cursor-pointer group border-orange-200">
                  <CardHeader>
                    <div className="flex justify-between items-start mb-2">
                      <Badge variant="secondary" className="text-xs bg-orange-100 text-orange-800">
                        {guide.category}
                      </Badge>
                      <Badge className={getDifficultyColor(guide.difficulty)}>
                        {guide.difficulty}
                      </Badge>
                    </div>
                    <CardTitle className="text-xl group-hover:text-orange-600 transition-colors">
                      {guide.title}
                    </CardTitle>
                    <CardDescription>
                      {guide.description}
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="flex items-center justify-between text-sm text-gray-500 mb-4">
                      <div className="flex items-center space-x-4">
                        <div className="flex items-center space-x-1">
                          <Clock className="h-4 w-4" />
                          <span>{guide.readTime}</span>
                        </div>
                        <div className="flex items-center space-x-1">
                          <User className="h-4 w-4" />
                          <span>{guide.author}</span>
                        </div>
                      </div>
                      <div className="flex items-center space-x-1">
                        <Star className="h-4 w-4 text-yellow-500 fill-current" />
                        <span>{guide.rating}</span>
                      </div>
                    </div>
                    
                    <div className="flex flex-wrap gap-1 mb-4">
                      {guide.tags.slice(0, 4).map((tag, index) => (
                        <Badge key={index} variant="outline" className="text-xs">
                          {tag}
                        </Badge>
                      ))}
                    </div>

                    <Button className="w-full group-hover:bg-orange-600 transition-colors">
                      Ler Guia
                      <ArrowRight className="ml-2 h-4 w-4" />
                    </Button>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        </section>
      )}

      {/* All Guides */}
      <section className="py-8 px-4">
        <div className="max-w-7xl mx-auto">
          <div className="flex justify-between items-center mb-6">
            <h2 className="text-2xl font-bold text-gray-900">
              {searchTerm ? `${filteredGuides.length} guias encontrados` : 'Todos os Guias'}
            </h2>
            <div className="flex items-center text-sm text-gray-500">
              <TrendingUp className="h-4 w-4 mr-1" />
              Ordenado por popularidade
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {filteredGuides.map((guide) => (
              <Card key={guide.id} className="hover:shadow-lg transition-shadow cursor-pointer group">
                <CardHeader>
                  <div className="flex justify-between items-start mb-2">
                    <Badge variant="secondary" className="text-xs">
                      {guide.category}
                    </Badge>
                    <Badge className={getDifficultyColor(guide.difficulty)}>
                      {guide.difficulty}
                    </Badge>
                  </div>
                  <CardTitle className="text-lg group-hover:text-orange-600 transition-colors">
                    {guide.title}
                  </CardTitle>
                  <CardDescription className="text-sm">
                    {guide.description}
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="flex items-center justify-between text-sm text-gray-500 mb-4">
                    <div className="flex items-center space-x-1">
                      <Clock className="h-4 w-4" />
                      <span>{guide.readTime}</span>
                    </div>
                    <div className="flex items-center space-x-1">
                      <Star className="h-4 w-4 text-yellow-500 fill-current" />
                      <span>{guide.rating}</span>
                    </div>
                  </div>
                  
                  <div className="flex flex-wrap gap-1 mb-4">
                    {guide.tags.slice(0, 3).map((tag, index) => (
                      <Badge key={index} variant="outline" className="text-xs">
                        {tag}
                      </Badge>
                    ))}
                    {guide.tags.length > 3 && (
                      <Badge variant="outline" className="text-xs">
                        +{guide.tags.length - 3}
                      </Badge>
                    )}
                  </div>

                  <Button className="w-full group-hover:bg-orange-600 transition-colors">
                    Ler Guia
                    <ArrowRight className="ml-2 h-4 w-4" />
                  </Button>
                </CardContent>
              </Card>
            ))}
          </div>

          {filteredGuides.length === 0 && (
            <div className="text-center py-12">
              <div className="text-gray-400 mb-4">
                <Search className="h-16 w-16 mx-auto" />
              </div>
              <h3 className="text-xl font-semibold text-gray-900 mb-2">
                Nenhum guia encontrado
              </h3>
              <p className="text-gray-600">
                Tente ajustar seu termo de busca
              </p>
            </div>
          )}
        </div>
      </section>
    </div>
  );
};

export default Guias;
