import { Star, Mail, MessageCircle, Facebook, Twitter, Linkedin, Instagram, Youtube } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Link } from "react-router-dom";
import { useTranslation } from "react-i18next";
import SocialShare from "./SocialShare";

const Footer = () => {
  const { t } = useTranslation();
  const currentYear = new Date().getFullYear();

  const footerLinks = {
    recursos: [
      { name: "Prompts de IA", href: "/prompts" },
      { name: "Ferramentas IA", href: "/ferramentas-ia" },
      { name: "No-Code", href: "/no-code" },
      { name: "<PERSON><PERSON><PERSON>", href: "/guias" },
      { name: "Glossário", href: "/glossario" },
      { name: "Blog", href: "/blog" }
    ],
    categorias: [
      { name: "Marketing Digital", href: "/prompts?category=Marketing" },
      { name: "Produtividade", href: "/prompts?category=Produtividade" },
      { name: "Copywriting", href: "/prompts?category=Copywriting" },
      { name: "SEO", href: "/prompts?category=SEO" },
      { name: "Automação", href: "/ferramentas-ia?category=Automação" }
    ],
    empresa: [
      { name: "Sobre Nós", href: "/sobre" },
      { name: "Contato", href: "/contato" },
      { name: "Política de Privacidade", href: "/privacidade" },
      { name: "Termos de Uso", href: "/termos" },
      { name: "FAQ", href: "/faq" }
    ]
  };

  const socialLinks = [
    { 
      name: "WhatsApp", 
      icon: MessageCircle, 
      href: "https://wa.me/5511999999999?text=Olá! Vim do Prompt Genius AI",
      color: "text-green-600 hover:text-green-700"
    },
    { 
      name: "Facebook", 
      icon: Facebook, 
      href: "https://facebook.com/promptgeniusai",
      color: "text-blue-600 hover:text-blue-700"
    },
    { 
      name: "Twitter", 
      icon: Twitter, 
      href: "https://twitter.com/promptgeniusai",
      color: "text-sky-500 hover:text-sky-600"
    },
    { 
      name: "LinkedIn", 
      icon: Linkedin, 
      href: "https://linkedin.com/company/promptgeniusai",
      color: "text-blue-700 hover:text-blue-800"
    },
    { 
      name: "Instagram", 
      icon: Instagram, 
      href: "https://instagram.com/promptgeniusai",
      color: "text-pink-600 hover:text-pink-700"
    },
    { 
      name: "YouTube", 
      icon: Youtube, 
      href: "https://youtube.com/@promptgeniusai",
      color: "text-red-600 hover:text-red-700"
    }
  ];

  return (
    <footer className="bg-gray-900 text-white">
      {/* Main Footer */}
      <div className="max-w-7xl mx-auto px-4 py-16">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-8">
          {/* Brand Section */}
          <div className="lg:col-span-2">
            <div className="flex items-center space-x-2 mb-4">
              <div className="w-10 h-10 bg-gradient-to-br from-blue-600 to-purple-600 rounded-lg flex items-center justify-center">
                <Star className="h-6 w-6 text-white" />
              </div>
              <div>
                <h3 className="text-xl font-bold">Prompt Genius AI</h3>
                <p className="text-sm text-gray-400">Guia Total de IA</p>
              </div>
            </div>
            <p className="text-gray-300 mb-6 max-w-md">
              A maior e mais completa coleção de recursos de inteligência artificial em português. 
              +6000 prompts, ferramentas e guias para dominar a IA.
            </p>
            
            {/* Social Share */}
            <div className="mb-6">
              <p className="text-sm text-gray-400 mb-3">Compartilhe com seus amigos:</p>
              <SocialShare 
                title="Prompt Genius AI - +6000 Recursos de IA"
                description="Descubra a maior coleção de recursos de IA em português"
                hashtags={["IA", "ChatGPT", "Prompts", "NoCode", "Produtividade"]}
              />
            </div>

            {/* Newsletter */}
            <div>
              <h4 className="font-semibold mb-3">📧 Newsletter Semanal</h4>
              <p className="text-sm text-gray-400 mb-3">
                Receba os melhores prompts e novidades sobre IA
              </p>
              <div className="flex gap-2">
                <input
                  type="email"
                  placeholder="Seu melhor email"
                  className="flex-1 px-3 py-2 bg-gray-800 border border-gray-700 rounded-md text-white placeholder-gray-400 focus:outline-none focus:border-blue-500"
                />
                <Button className="bg-blue-600 hover:bg-blue-700">
                  <Mail className="h-4 w-4" />
                </Button>
              </div>
            </div>
          </div>

          {/* Resources Links */}
          <div>
            <h4 className="font-semibold mb-4">Recursos</h4>
            <ul className="space-y-2">
              {footerLinks.recursos.map((link) => (
                <li key={link.name}>
                  <Link 
                    to={link.href} 
                    className="text-gray-400 hover:text-white transition-colors text-sm"
                  >
                    {link.name}
                  </Link>
                </li>
              ))}
            </ul>
          </div>

          {/* Categories Links */}
          <div>
            <h4 className="font-semibold mb-4">Categorias</h4>
            <ul className="space-y-2">
              {footerLinks.categorias.map((link) => (
                <li key={link.name}>
                  <Link 
                    to={link.href} 
                    className="text-gray-400 hover:text-white transition-colors text-sm"
                  >
                    {link.name}
                  </Link>
                </li>
              ))}
            </ul>
          </div>

          {/* Company Links */}
          <div>
            <h4 className="font-semibold mb-4">Empresa</h4>
            <ul className="space-y-2 mb-6">
              {footerLinks.empresa.map((link) => (
                <li key={link.name}>
                  <Link 
                    to={link.href} 
                    className="text-gray-400 hover:text-white transition-colors text-sm"
                  >
                    {link.name}
                  </Link>
                </li>
              ))}
            </ul>

            {/* Social Media */}
            <div>
              <h4 className="font-semibold mb-3">Redes Sociais</h4>
              <div className="grid grid-cols-3 gap-2">
                {socialLinks.map((social) => {
                  const IconComponent = social.icon;
                  return (
                    <a
                      key={social.name}
                      href={social.href}
                      target="_blank"
                      rel="noopener noreferrer"
                      className={`p-2 bg-gray-800 rounded-md hover:bg-gray-700 transition-colors ${social.color}`}
                      title={social.name}
                    >
                      <IconComponent className="h-4 w-4" />
                    </a>
                  );
                })}
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Bottom Bar */}
      <div className="border-t border-gray-800">
        <div className="max-w-7xl mx-auto px-4 py-6">
          <div className="flex flex-col md:flex-row justify-between items-center">
            <div className="text-sm text-gray-400 mb-4 md:mb-0">
              © {currentYear} Prompt Genius AI. Todos os direitos reservados.
            </div>
            <div className="flex items-center space-x-6 text-sm text-gray-400">
              <span>🇧🇷 Feito no Brasil com ❤️</span>
              <span>•</span>
              <span>+6000 recursos de IA</span>
              <span>•</span>
              <span>Atualizado diariamente</span>
            </div>
          </div>
        </div>
      </div>
    </footer>
  );
};

export default Footer;
