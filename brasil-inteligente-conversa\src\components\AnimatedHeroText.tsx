import { useState, useEffect } from "react";
import { useTranslation } from "react-i18next";

interface AnimatedHeroTextProps {
  className?: string;
}

const AnimatedHeroText = ({ className = "" }: AnimatedHeroTextProps) => {
  const { t } = useTranslation();
  const [currentIndex, setCurrentIndex] = useState(0);
  const [isVisible, setIsVisible] = useState(true);

  // Get SEO keywords from translation
  const seoKeywords = t('seoKeywords', { returnObjects: true }) as string[];
  const prefix = t('heroAnimated.prefix');
  const suffix = t('heroAnimated.suffix');

  useEffect(() => {
    const interval = setInterval(() => {
      setIsVisible(false);
      
      setTimeout(() => {
        setCurrentIndex((prevIndex) => (prevIndex + 1) % seoKeywords.length);
        setIsVisible(true);
      }, 300); // Half of the transition duration
      
    }, 3000); // Change every 3 seconds

    return () => clearInterval(interval);
  }, [seoKeywords.length]);

  if (!seoKeywords || seoKeywords.length === 0) {
    return (
      <h1 className={`text-4xl md:text-6xl font-bold text-gray-900 leading-tight ${className}`}>
        {prefix} Inteligência Artificial {suffix}
      </h1>
    );
  }

  return (
    <h1 className={`text-4xl md:text-6xl font-bold text-gray-900 leading-tight ${className}`}>
      {prefix}{" "}
      <span className="relative inline-block">
        <span
          className={`text-transparent bg-clip-text bg-gradient-to-r from-blue-600 to-purple-600 transition-all duration-500 ${
            isVisible ? 'opacity-100 transform translate-y-0' : 'opacity-0 transform -translate-y-2'
          }`}
          style={{ minWidth: '300px', display: 'inline-block' }}
        >
          {seoKeywords[currentIndex]}
        </span>
      </span>
      <br />
      <span className="text-transparent bg-clip-text bg-gradient-to-r from-blue-600 to-purple-600">
        {suffix}
      </span>
    </h1>
  );
};

export default AnimatedHeroText;
