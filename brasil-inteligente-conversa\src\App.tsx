import { Toaster } from "@/components/ui/toaster";
import { Toaster as Sonner } from "@/components/ui/sonner";
import { TooltipProvider } from "@/components/ui/tooltip";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { BrowserRouter, Routes, Route } from "react-router-dom";
import Index from "./pages/Index";
import Prompts from "./pages/Prompts";
import FerramentasIA from "./pages/FerramentasIA";
import NoCode from "./pages/NoCode";
import Guias from "./pages/Guias";
import Glossario from "./pages/Glossario";
import Blog from "./pages/Blog";
import NotFound from "./pages/NotFound";

const queryClient = new QueryClient();

const App = () => (
  <QueryClientProvider client={queryClient}>
    <TooltipProvider>
      <Toaster />
      <Sonner />
      <BrowserRouter>
        <Routes>
          <Route path="/" element={<Index />} />
          <Route path="/prompts" element={<Prompts />} />
          <Route path="/ferramentas-ia" element={<FerramentasIA />} />
          <Route path="/no-code" element={<NoCode />} />
          <Route path="/guias" element={<Guias />} />
          <Route path="/glossario" element={<Glossario />} />
          <Route path="/blog" element={<Blog />} />
          {/* ADD ALL CUSTOM ROUTES ABOVE THE CATCH-ALL "*" ROUTE */}
          <Route path="*" element={<NotFound />} />
        </Routes>
      </BrowserRouter>
    </TooltipProvider>
  </QueryClientProvider>
);

export default App;
