import { useState } from "react";
import { Share2, Facebook, Twitter, Linkedin, Link as LinkIcon, MessageCircle, Co<PERSON>, Check } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";

interface SocialShareProps {
  url?: string;
  title?: string;
  description?: string;
  hashtags?: string[];
  className?: string;
}

const SocialShare = ({ 
  url = window.location.href, 
  title = "Prompt Genius AI - Guia Total de IA", 
  description = "Descubra +6000 recursos de IA: prompts, ferramentas e guias completos",
  hashtags = ["IA", "ChatGPT", "Prompts", "NoCode", "Produtividade"],
  className = ""
}: SocialShareProps) => {
  const [copied, setCopied] = useState(false);

  const encodedUrl = encodeURIComponent(url);
  const encodedTitle = encodeURIComponent(title);
  const encodedDescription = encodeURIComponent(description);
  const hashtagString = hashtags.map(tag => `#${tag}`).join(' ');

  const shareLinks = {
    facebook: `https://www.facebook.com/sharer/sharer.php?u=${encodedUrl}`,
    twitter: `https://twitter.com/intent/tweet?url=${encodedUrl}&text=${encodedTitle}&hashtags=${hashtags.join(',')}`,
    linkedin: `https://www.linkedin.com/sharing/share-offsite/?url=${encodedUrl}`,
    whatsapp: `https://wa.me/?text=${encodedTitle}%20${encodedUrl}`,
  };

  const copyToClipboard = async () => {
    try {
      await navigator.clipboard.writeText(url);
      setCopied(true);
      setTimeout(() => setCopied(false), 2000);
    } catch (err) {
      console.error('Failed to copy: ', err);
    }
  };

  const openShare = (platform: keyof typeof shareLinks) => {
    window.open(shareLinks[platform], '_blank', 'width=600,height=400');
  };

  return (
    <div className={className}>
      <Popover>
        <PopoverTrigger asChild>
          <Button variant="outline" size="sm" className="gap-2">
            <Share2 className="h-4 w-4" />
            Compartilhar
          </Button>
        </PopoverTrigger>
        <PopoverContent className="w-80">
          <div className="space-y-4">
            <div>
              <h4 className="font-semibold text-sm mb-2">Compartilhar Prompt Genius AI</h4>
              <p className="text-xs text-gray-600 mb-3">{description}</p>
            </div>
            
            <div className="grid grid-cols-2 gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => openShare('facebook')}
                className="gap-2 justify-start"
              >
                <Facebook className="h-4 w-4 text-blue-600" />
                Facebook
              </Button>
              
              <Button
                variant="outline"
                size="sm"
                onClick={() => openShare('twitter')}
                className="gap-2 justify-start"
              >
                <Twitter className="h-4 w-4 text-sky-500" />
                Twitter
              </Button>
              
              <Button
                variant="outline"
                size="sm"
                onClick={() => openShare('linkedin')}
                className="gap-2 justify-start"
              >
                <Linkedin className="h-4 w-4 text-blue-700" />
                LinkedIn
              </Button>
              
              <Button
                variant="outline"
                size="sm"
                onClick={() => openShare('whatsapp')}
                className="gap-2 justify-start"
              >
                <MessageCircle className="h-4 w-4 text-green-600" />
                WhatsApp
              </Button>
            </div>
            
            <div className="border-t pt-3">
              <div className="flex items-center gap-2">
                <div className="flex-1 bg-gray-50 rounded px-3 py-2 text-sm text-gray-600 truncate">
                  {url}
                </div>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={copyToClipboard}
                  className="gap-1"
                >
                  {copied ? (
                    <>
                      <Check className="h-4 w-4 text-green-600" />
                      Copiado!
                    </>
                  ) : (
                    <>
                      <Copy className="h-4 w-4" />
                      Copiar
                    </>
                  )}
                </Button>
              </div>
            </div>
            
            <div className="text-xs text-gray-500">
              <p className="font-medium mb-1">Hashtags sugeridas:</p>
              <p>{hashtagString}</p>
            </div>
          </div>
        </PopoverContent>
      </Popover>
    </div>
  );
};

export default SocialShare;
