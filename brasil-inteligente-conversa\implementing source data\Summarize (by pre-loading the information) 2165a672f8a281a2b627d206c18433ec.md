# Summarize (by pre-loading the information)

Tags: learn

There are situations where we need to summarize a report or a private document that is not publicly available.

The way to do this is by pre-loading the information into the chat using the following prompt 👇

```
I will provide you with a text to summarize:
['INSERT TEXT']
Please generate a concise and comprehensive summary of the text provided.
```

If you have a longer text to summarize, you might have to break it down into chunks. This is due to the token limit that ChatGPT can handle in a single interaction.

<aside>
💡 ChatGPT operates based on tokens, which are chunks of text that the model reads and writes. A token can be as short as one character or as long as one word.

For example, "ChatGPT" is one token, while "a" and "!" are also one token each. 

As of the date of writing this, the token limit for ChatGPT-4 is 8K and ChatGPT-3.5 is 4K.

</aside>

Here is the prompt you can use to summarize longer text👇

```
I will provide you with a text in chunks. Your task is to wait for all the chunks and then follow further instructions. Please confirm that you understood this prompt.
```

After ChatGPT confirms that it is ready to receive the text, input your text in chunks and instruct ChatGPT to summarize the text 👇

```
Generate a concise and comprehensive summary of the text provided. 
```