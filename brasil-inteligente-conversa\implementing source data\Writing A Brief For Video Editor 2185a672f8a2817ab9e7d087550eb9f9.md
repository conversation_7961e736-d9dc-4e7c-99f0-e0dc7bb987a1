# Writing A Brief For Video Editor

### About

ChatGPT is like a storyteller sorcerer, but instead of weaving tales, it creates video briefs! With ChatGPT, you can bid farewell to writer's block and say hello to impressing video editors. It's like having your own <PERSON><PERSON><PERSON><PERSON> working for you, but instead of writing books, it writes video briefs that will make your video editor say "Wingardium Leviosa!" (or something like that). Just don't be surprised if ChatGPT starts talking about Hogwarts and Quidditch.

### Prompts

```jsx
"Can you write a brief for a **[video style/type]** that will be used to promote our **[product/service/brand]** to a **[target audience]**?"
```

```jsx
"Can you draft a brief for a **[video length/duration]** that will be used to explain our **[company's mission/values]** to potential clients?"
```

```jsx
"Can you create a brief for a **[explainer/how-to]** video that will be used to demonstrate the steps to use our **[product/service]** effectively to a **[new/existing customers]"**
```

```jsx
"I am planning to create a video that will educate my audience about **[topic]**. Can you help me write a brief for the video editor that includes information on tone, pacing, and desired outcome of the video?"
```

```jsx
"I need to create a video that will showcase the features of **[product/service]**. Can you help me write a brief for the video editor that highlights the key selling points and differentiates our offering from competitors?"
```

### Examples

![Screenshot_20230128_091031.png](Screenshot_20230128_091031.png)

### Tips

<aside>
💡 **Be specific about the project:** Provide ChatGPT with detailed information about the video project, including the purpose, target audience, and desired tone of the video. This will help ChatGPT provide more relevant and accurate information for the brief.

</aside>

<aside>
💡 **Highlight important details:** Use ChatGPT to help you highlight important details, such as specific shots or elements that are critical to the project.

</aside>