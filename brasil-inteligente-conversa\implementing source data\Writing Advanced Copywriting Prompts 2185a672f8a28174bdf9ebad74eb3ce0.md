# Writing Advanced Copywriting Prompts

### FILL-IN-THE-BLANK **PROMPTS:**

```jsx
Using the PAS copywriting formula, create a 500 word landing page that persuades potential buyers to purchase **[product]**. Use scarcity by saying you only have 3 units left, and include a short story about how one client went from **[state a]** to **[state b]**.
```

```jsx
Use the 5 Basic Objections framework to write a product description for **[product]** that helps **[ideal client]** achieve **[dream outcome]**. Address these common objections of a potential customer:
**[Objection 1]
[Objection 2]
[Objection 3]
[Objection 4]
[Objection 5]**

Finish by listing all the negative consequences of not taking action now.
```

```jsx
Write a 5-step soap opera email sequence about how attending **[event]** will change **[ideal customer]** life. Include these benefits:
**[Benefit 1]
[Benefit 2]
[Benefit 3]**
These pain points:
**[Pain point 1]
[Pain point 2]
[Pain point 3]**
And these testimonials:
**[Testimonial 1]
[Testimonial 2]
[Testimonial 3]**

Increase the urgency of signing up for the **[event]** progressively, starting with very little on email 1 and a lot on email 5. On email 5, include a final guarantee saying that if they attend, you will give them **[bonus]**.
```

```jsx
Use the AIDA copywriting framework to grab the attention of **[ideal customer]** and persuade them to **[call to action]**. Start with a question to get their attention, present statistics that shows how bad **[problem]** is, state these 3 benefits about our product **[benefit 1]**, **[benefit 2]**, **benefit 3]**, and ask for **[call to action].**
```

```jsx
Write a webinar script using the 'PASTOR' framework to address the pain points of **[ideal customer]** and present my **[product]** as the solution. Identify the problem they are facing, amplify the consequences of not solving it, tell this story related to the problem **[story]**, include these testimonials from happy customers **[testimonials]**, present our offer, and ask for a purchase.
```

```jsx
Write a webinar script using the perfect webinar formula by Russell Brunson. Promote a 8 week coaching program on **[topic]** that will help **[ideal client]** get out of **[pain points]** and achieve **[URL]**. Mention that the one thing to achieve all their dreams is **[unique mechanism]**, and that the only way to access it is by **[call to action]**.
```

### EXAMPLES:

![Advance Copywriting.png](Advance_Copywriting.png)