
import { useState } from "react";
import { <PERSON>, <PERSON>, <PERSON>ap, <PERSON>, Star, ArrowRight } from "lucide-react";
import { Input } from "@/components/ui/input";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import Header from "@/components/Header";
import Hero from "@/components/Hero";
import ResourceCard from "@/components/ResourceCard";
import CategorySection from "@/components/CategorySection";
import { prompts, aiTools, noCodeTools, searchAll, getStats } from "@/data/index";

const Index = () => {
  const [searchTerm, setSearchTerm] = useState("");
  const stats = getStats();

  // Use comprehensive search when there's a search term
  const searchResults = searchTerm ? searchAll(searchTerm) : [];

  // Filter by type for display
  const filteredPrompts = searchTerm
    ? searchResults.filter(result => result.type === 'prompt').map(result => result.originalItem)
    : prompts.slice(0, 8); // Show first 8 when no search

  const filteredAiTools = searchTerm
    ? searchResults.filter(result => result.type === 'aiTool').map(result => result.originalItem)
    : aiTools.slice(0, 8); // Show first 8 when no search

  const filteredNoCodeTools = searchTerm
    ? searchResults.filter(result => result.type === 'noCodeTool').map(result => result.originalItem)
    : noCodeTools.slice(0, 8); // Show first 8 when no search

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100">
      <Header />
      <Hero />
      
      {/* Search Section */}
      <section className="py-12 px-4">
        <div className="max-w-4xl mx-auto">
          <div className="relative">
            <Search className="absolute left-4 top-1/2 transform -translate-y-1/2 text-gray-400 h-5 w-5" />
            <Input
              type="text"
              placeholder="Busque por prompts, ferramentas de IA ou no-code..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-12 h-14 text-lg border-2 border-gray-200 focus:border-blue-500 bg-white/80 backdrop-blur-sm"
            />
          </div>
        </div>
      </section>

      {/* Main Content */}
      <div className="max-w-7xl mx-auto px-4 pb-20">
        {/* Prompts Section */}
        <CategorySection
          title={`${stats.totalPrompts}+ Prompts de IA`}
          description="Prompts testados e otimizados para maximizar seus resultados"
          icon={<Brain className="h-8 w-8 text-blue-600" />}
          items={filteredPrompts}
          type="prompt"
        />

        {/* AI Tools Section */}
        <CategorySection
          title={`${stats.totalAITools}+ Ferramentas de IA`}
          description="As melhores ferramentas de inteligência artificial do mercado"
          icon={<Zap className="h-8 w-8 text-purple-600" />}
          items={filteredAiTools}
          type="tool"
        />

        {/* No-Code Tools Section */}
        <CategorySection
          title={`${stats.totalNoCodeTools}+ Ferramentas No-Code`}
          description="Construa sem programar com estas ferramentas poderosas"
          icon={<Code className="h-8 w-8 text-green-600" />}
          items={filteredNoCodeTools}
          type="nocode"
        />
      </div>
    </div>
  );
};

export default Index;
