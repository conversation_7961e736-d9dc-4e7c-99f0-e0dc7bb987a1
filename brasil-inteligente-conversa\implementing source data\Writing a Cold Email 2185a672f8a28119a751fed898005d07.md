# Writing a Cold Email

### FILL-IN-THE-BLANK **PROMPTS:**

```jsx
Write an email for a **[describe prospect]** who is struggling with **[pain points]** and wants to **[desire]**. Mention that my offer is **[describe offer]**, the guarantee is **[insert guarantee]**, and my credentials are **[insert credentials]**. Invite them to book a call with me. Use a friendly tone and make the email short
```

```jsx
Write a 200-word cold email that includes:

Greeting: Hello **[name]**
Offer: We can get you 10 appointments in the next week, or you don't pay
Credentials: We have worked with 83 clients in your same niche
Call to action: If you are interested, hit reply and I'll send over my calendar
```

### QUESTION-BASED **PROMPTS:**

1. “Can you write a cold email for potential customers that starts with "Dear [Prospective Customer]?”
2. “Write a cold email for potential customers that includes a sentence about our company, "We're [Company Name], and we specialize in [Company Expertise]."
3. “Can you create a cold email for potential customers that explains the unique benefits of our products/services?”
4. “Write a cold email for potential customers that includes a special offer, "Take advantage of our [Special Offer] today!"
5. “Can you craft a cold email for potential customers that emphasizes our commitment to customer satisfaction?”
6. “Write a cold email for potential customers that includes a call to action, "Get in touch with us today to learn more!"
7. “Can you write a cold email for potential customers that highlights our company's values and mission?”
8. “Write a cold email for potential customers that features customer testimonials, "See what our satisfied customers are saying about us..."
9. “Can you create a cold email for potential customers that explains why they should choose us over our competitors?”
10. “Write a cold email for potential customers that ends with a personalized message, "We're excited to work with you, [Prospective Customer]!"

### EXAMPLES:

![Writing a Cold Email.png](Writing_a_Cold_Email.png)