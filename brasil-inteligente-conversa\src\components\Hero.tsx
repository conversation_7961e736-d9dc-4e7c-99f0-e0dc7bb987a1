
import { ArrowR<PERSON>, Play, Users, Star, TrendingUp } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { Link } from "react-router-dom";
import { useTranslation } from "react-i18next";
import { getStats } from "@/data/index";
import SocialShare from "./SocialShare";
import AnimatedHeroText from "./AnimatedHeroText";

const Hero = () => {
  const { t } = useTranslation();
  const stats = getStats();

  return (
    <section className="relative py-20 px-4 overflow-hidden">
      <div className="absolute inset-0 bg-gradient-to-br from-blue-600/20 via-purple-600/20 to-indigo-600/20"></div>
      <div className="relative max-w-6xl mx-auto text-center">
        <div className="mb-8">
          <span className="inline-flex items-center px-4 py-2 bg-blue-100 text-blue-800 rounded-full text-sm font-medium mb-6">
            <Star className="h-4 w-4 mr-2" />
            +{stats.totalItems.toLocaleString()} {t('hero.title').includes('6000') ? 'AI resources available' : 'recursos de IA disponíveis'}
          </span>
        </div>
        
        <AnimatedHeroText className="mb-6" />
        
        <p className="text-xl md:text-2xl text-gray-600 mb-8 max-w-4xl mx-auto leading-relaxed">
          <strong>{stats.totalPrompts}+ {t('stats.totalPrompts')}</strong> • <strong>{stats.totalAITools}+ {t('stats.totalAITools')}</strong> • <strong>{stats.totalNoCodeTools}+ {t('stats.totalNoCodeTools')}</strong>
        </p>

        <p className="text-lg text-gray-600 mb-12 max-w-3xl mx-auto">
          {t('hero.description')}
        </p>

        <div className="flex flex-col sm:flex-row gap-4 justify-center items-center mb-8">
          <Link to="/prompts">
            <Button size="lg" className="bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-lg px-8 py-4">
              {t('hero.exploreResources')}
              <ArrowRight className="ml-2 h-5 w-5" />
            </Button>
          </Link>
          <Link to="/glossario">
            <Button variant="outline" size="lg" className="text-lg px-8 py-4 border-gray-300 hover:bg-gray-50">
              <Play className="mr-2 h-5 w-5" />
              {t('hero.viewGlossary')}
            </Button>
          </Link>
        </div>

        {/* Social Share */}
        <div className="flex justify-center mb-16">
          <SocialShare
            title={`${t('header.title')} - +6000 ${t('stats.totalAITools')}`}
            description={t('hero.description')}
            hashtags={["IA", "ChatGPT", "Prompts", "NoCode", "Produtividade", "FerramentasIA"]}
          />
        </div>

        {/* Stats */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-8 max-w-4xl mx-auto">
          <div className="text-center">
            <div className="text-3xl font-bold text-gray-900 mb-2">{stats.totalPrompts}+</div>
            <div className="text-gray-600">Prompts de IA</div>
          </div>
          <div className="text-center">
            <div className="text-3xl font-bold text-gray-900 mb-2">{stats.totalAITools}+</div>
            <div className="text-gray-600">Ferramentas IA</div>
          </div>
          <div className="text-center">
            <div className="text-3xl font-bold text-gray-900 mb-2">{stats.totalNoCodeTools}+</div>
            <div className="text-gray-600">No-Code Tools</div>
          </div>
          <div className="text-center">
            <div className="text-3xl font-bold text-gray-900 mb-2">{Math.floor(stats.totalUses / 1000)}k+</div>
            <div className="text-gray-600">Usuários Ativos</div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default Hero;
