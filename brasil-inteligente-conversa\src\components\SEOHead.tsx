import { useEffect } from 'react';

interface SEOHeadProps {
  title?: string;
  description?: string;
  keywords?: string[];
  ogTitle?: string;
  ogDescription?: string;
  ogImage?: string;
  ogUrl?: string;
  twitterCard?: string;
  canonicalUrl?: string;
  structuredData?: object;
}

const SEOHead = ({
  title = "Prompt Genius AI - Guia Total de IA com +6000 Recursos",
  description = "Descubra +6000 recursos de IA: prompts testados, ferramentas premium, guias no-code e glossário completo. Domine <PERSON>t<PERSON>, Claude, Midjourney e muito mais.",
  keywords = [
    "prompt genius ai", "prompts chatgpt", "ferramentas ia", "inteligência artificial", 
    "chatgpt prompts", "ia produtividade", "no-code", "automação", "midjourney", 
    "claude ai", "gemini", "prompt engineering", "copywriting ia", "seo ia",
    "marketing digital ia", "glossário ia", "guias ia", "brasil inteligente"
  ],
  ogTitle,
  ogDescription,
  ogImage = "/og-image.jpg",
  ogUrl,
  twitterCard = "summary_large_image",
  canonicalUrl,
  structuredData
}: SEOHeadProps) => {
  
  useEffect(() => {
    // Set document title
    document.title = title;

    // Set or update meta tags
    const setMetaTag = (name: string, content: string, property?: boolean) => {
      const attribute = property ? 'property' : 'name';
      let meta = document.querySelector(`meta[${attribute}="${name}"]`) as HTMLMetaElement;
      
      if (!meta) {
        meta = document.createElement('meta');
        meta.setAttribute(attribute, name);
        document.head.appendChild(meta);
      }
      
      meta.content = content;
    };

    // Basic meta tags
    setMetaTag('description', description);
    setMetaTag('keywords', keywords.join(', '));
    setMetaTag('author', 'Prompt Genius AI');
    setMetaTag('robots', 'index, follow');
    setMetaTag('language', 'pt-BR');
    setMetaTag('revisit-after', '7 days');

    // Open Graph tags
    setMetaTag('og:title', ogTitle || title, true);
    setMetaTag('og:description', ogDescription || description, true);
    setMetaTag('og:image', ogImage, true);
    setMetaTag('og:url', ogUrl || window.location.href, true);
    setMetaTag('og:type', 'website', true);
    setMetaTag('og:site_name', 'Prompt Genius AI', true);
    setMetaTag('og:locale', 'pt_BR', true);

    // Twitter Card tags
    setMetaTag('twitter:card', twitterCard);
    setMetaTag('twitter:title', ogTitle || title);
    setMetaTag('twitter:description', ogDescription || description);
    setMetaTag('twitter:image', ogImage);
    setMetaTag('twitter:site', '@promptgeniusai');
    setMetaTag('twitter:creator', '@promptgeniusai');

    // Additional SEO tags
    setMetaTag('theme-color', '#2563eb');
    setMetaTag('msapplication-TileColor', '#2563eb');
    setMetaTag('apple-mobile-web-app-capable', 'yes');
    setMetaTag('apple-mobile-web-app-status-bar-style', 'default');

    // Canonical URL
    let canonicalLink = document.querySelector('link[rel="canonical"]') as HTMLLinkElement;
    if (!canonicalLink) {
      canonicalLink = document.createElement('link');
      canonicalLink.rel = 'canonical';
      document.head.appendChild(canonicalLink);
    }
    canonicalLink.href = canonicalUrl || window.location.href;

    // Structured Data (JSON-LD)
    if (structuredData) {
      let script = document.querySelector('script[type="application/ld+json"]');
      if (!script) {
        script = document.createElement('script');
        script.type = 'application/ld+json';
        document.head.appendChild(script);
      }
      script.textContent = JSON.stringify(structuredData);
    } else {
      // Default structured data for the website
      const defaultStructuredData = {
        "@context": "https://schema.org",
        "@type": "WebSite",
        "name": "Prompt Genius AI",
        "description": description,
        "url": window.location.origin,
        "potentialAction": {
          "@type": "SearchAction",
          "target": {
            "@type": "EntryPoint",
            "urlTemplate": `${window.location.origin}/prompts?search={search_term_string}`
          },
          "query-input": "required name=search_term_string"
        },
        "publisher": {
          "@type": "Organization",
          "name": "Prompt Genius AI",
          "description": "Plataforma completa de recursos de Inteligência Artificial",
          "url": window.location.origin
        },
        "mainEntity": {
          "@type": "ItemList",
          "name": "Recursos de IA",
          "description": "Coleção completa de prompts, ferramentas e guias de IA",
          "numberOfItems": "6000+",
          "itemListElement": [
            {
              "@type": "ListItem",
              "position": 1,
              "name": "Prompts de IA",
              "description": "Prompts testados para ChatGPT, Claude e outras IAs"
            },
            {
              "@type": "ListItem", 
              "position": 2,
              "name": "Ferramentas de IA",
              "description": "Ferramentas premium de inteligência artificial"
            },
            {
              "@type": "ListItem",
              "position": 3,
              "name": "Guias No-Code",
              "description": "Tutoriais completos de desenvolvimento sem código"
            },
            {
              "@type": "ListItem",
              "position": 4,
              "name": "Glossário de IA",
              "description": "Dicionário completo de termos de inteligência artificial"
            }
          ]
        }
      };

      let script = document.querySelector('script[type="application/ld+json"]');
      if (!script) {
        script = document.createElement('script');
        script.type = 'application/ld+json';
        document.head.appendChild(script);
      }
      script.textContent = JSON.stringify(defaultStructuredData);
    }

    // Preconnect to external domains for performance
    const preconnectDomains = [
      'https://fonts.googleapis.com',
      'https://fonts.gstatic.com',
      'https://www.google-analytics.com'
    ];

    preconnectDomains.forEach(domain => {
      let link = document.querySelector(`link[rel="preconnect"][href="${domain}"]`);
      if (!link) {
        link = document.createElement('link');
        link.rel = 'preconnect';
        link.href = domain;
        document.head.appendChild(link);
      }
    });

  }, [title, description, keywords, ogTitle, ogDescription, ogImage, ogUrl, twitterCard, canonicalUrl, structuredData]);

  return null; // This component doesn't render anything
};

export default SEOHead;
