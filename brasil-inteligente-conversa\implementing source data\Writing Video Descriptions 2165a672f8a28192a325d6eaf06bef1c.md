# Writing Video Descriptions

### About

ChatGPT is the ultimate video description writer! With its ability to understand context, it can craft descriptions that not only accurately describes the video content but also entice viewers to click and watch. With ChatGPT, you'll never have to worry about boring video descriptions again. It's like having a virtual video description ninja, who can write compelling descriptions in a blink of an eye. Whether you're looking for a short and snappy or a longer and more detailed description, ChatGPT has got you covered.

### Prompts

```jsx
"Please provide a video description for a **[specific type of video]** that includes **[key elements/stands out/encourages]** viewers to watch the video."
```

```jsx
"Write a video description for **[specific type of video]** that uses emotional appeal and storytelling to improve its effectiveness."
```

```jsx
"Generate a video description for a video that is about **[specific industry/field]** that includes words or phrases to increase engagement and views."
```

```jsx
"Generate a video description for a video that is about **[specific industry/field]** that includes words or phrases to increase engagement and views."
```

```jsx
"I am creating a promotional video for **[business/event]**. Can you help me write a video description that captures the essence of the event and encourages people to attend?"
```

### Examples

![Screenshot_20230129_090655.png](Screenshot_20230129_090655.png)

![Screenshot_20230129_090648.png](Screenshot_20230129_090648.png)

### Tips

<aside>
💡 **Provide an overall goal of the video:** Provide ChatGPT with an overall goal for the video, such as increasing views or engagement, so it can tailor its descriptions accordingly.

</aside>

<aside>
💡 **Provide examples of similar videos or competitors:** Provide ChatGPT with examples of similar videos or competitors in your industry to help it come up with descriptions that stand out from the competition.

</aside>

<aside>
💡 **Highlight key elements of the video:** Provide ChatGPT with information about key elements of the video, such as the main characters, locations, and plot, to help it come up with descriptions that accurately reflect the content of the video..

</aside>