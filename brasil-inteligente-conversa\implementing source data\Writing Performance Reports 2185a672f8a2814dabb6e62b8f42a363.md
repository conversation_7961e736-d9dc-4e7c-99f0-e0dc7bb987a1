# Writing Performance Reports

### About

<PERSON>t<PERSON><PERSON> is like a personal assistant, but instead of making coffee, it's making reports! With ChatGPT's language skills, you'll be able to craft the perfect performance report, without breaking a sweat. It's like having your own personal <PERSON>, but instead of making jokes, it's making charts and instead of saying "That's what she said" it's saying "Your performance exceeded expectations". Just don't be surprised if ChatGPT starts giving you virtual high-fives and fist bumps for the great job it did in writing the report.

### Prompts

```jsx
"What are the key metrics and data points that should be included in a performance report for **[specific industry/field]**?"
```

```jsx
"How do I write an executive summary for a performance report on **[specific company/project]**?"
```

```jsx
"What are some common mistakes to avoid when writing a performance report for **[specific industry/field]**?"
```

```jsx
"Can you provide a checklist for ensuring the completeness and accuracy of a performance report for **[specific company/department]**"
```

```jsx
"I need to write a performance report about **[about]** for **[department/team]** and I am struggling to come up with the right metrics to include. Can you help me identify key performance indicators that would be most relevant and useful for evaluating the team's performance?"
```

### Examples

![Screenshot_20230129_080632.png](Screenshot_20230129_080632.png)

![Screenshot_20230129_080700.png](Screenshot_20230129_080700.png)

### Tips

<aside>
💡 Clearly define the goals: Provide ChatGPT with the goals of the performance report, such as highlighting key metrics or identifying areas for improvement. This will help ChatGPT provide more relevant and accurate information for the report.

</aside>

<aside>
💡 Use industry-specific language: Use industry-specific language and terminology to communicate your findings and recommendations in the report. ChatGPT can help you with this by providing relevant terminology and jargon.

</aside>