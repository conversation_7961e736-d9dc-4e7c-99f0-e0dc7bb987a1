import { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>h, <PERSON>R<PERSON> } from "lucide-react";
import { Input } from "@/components/ui/input";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import Header from "@/components/Header";
import SEOHead from "@/components/SEOHead";
import Footer from "@/components/Footer";

interface GlossaryTerm {
  id: number;
  term: string;
  definition: string;
  category: string;
  examples?: string[];
  relatedTerms?: string[];
  seoKeywords: string[];
}

const Glossario = () => {
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedLetter, setSelectedLetter] = useState("all");

  // Comprehensive glossary for SEO - AI and tech terms in Portuguese
  const glossaryTerms: GlossaryTerm[] = [
    {
      id: 1,
      term: "Inteligência Artificial (IA)",
      definition: "Tecnologia que permite que máquinas simulem a inteligência humana, incluindo aprendizado, raciocínio e autocorreção.",
      category: "IA Geral",
      examples: ["ChatGPT", "Claude", "Gemini"],
      relatedTerms: ["Machine Learning", "Deep Learning", "Redes Neurais"],
      seoKeywords: ["inteligência artificial", "IA", "artificial intelligence", "tecnologia IA"]
    },
    {
      id: 2,
      term: "Prompt",
      definition: "Instrução ou comando dado a uma IA para gerar uma resposta específica. É a forma de comunicação entre humanos e sistemas de IA.",
      category: "Prompts",
      examples: ["Escreva um artigo sobre...", "Crie uma lista de...", "Analise este texto..."],
      relatedTerms: ["Prompt Engineering", "ChatGPT", "IA Generativa"],
      seoKeywords: ["prompt", "prompt IA", "comando IA", "instrução artificial"]
    },
    {
      id: 3,
      term: "Machine Learning",
      definition: "Subcampo da IA que permite que sistemas aprendam e melhorem automaticamente através da experiência sem serem explicitamente programados.",
      category: "IA Geral",
      examples: ["Algoritmos de recomendação", "Reconhecimento de imagem", "Processamento de linguagem natural"],
      relatedTerms: ["Deep Learning", "Algoritmos", "Dados de Treinamento"],
      seoKeywords: ["machine learning", "aprendizado de máquina", "ML", "algoritmos inteligentes"]
    },
    {
      id: 4,
      term: "No-Code",
      definition: "Abordagem de desenvolvimento que permite criar aplicações sem escrever código, usando interfaces visuais e ferramentas drag-and-drop.",
      category: "No-Code",
      examples: ["Bubble", "Webflow", "Zapier", "Airtable"],
      relatedTerms: ["Low-Code", "Desenvolvimento Visual", "Automação"],
      seoKeywords: ["no-code", "sem código", "desenvolvimento visual", "ferramentas no-code"]
    },
    {
      id: 5,
      term: "ChatGPT",
      definition: "Modelo de linguagem desenvolvido pela OpenAI, capaz de gerar texto semelhante ao humano e manter conversas naturais.",
      category: "Ferramentas IA",
      examples: ["GPT-3.5", "GPT-4", "ChatGPT Plus"],
      relatedTerms: ["OpenAI", "LLM", "IA Conversacional"],
      seoKeywords: ["chatgpt", "openai", "gpt", "chat inteligência artificial"]
    },
    {
      id: 6,
      term: "Automação",
      definition: "Processo de usar tecnologia para executar tarefas com mínima intervenção humana, aumentando eficiência e reduzindo erros.",
      category: "Automação",
      examples: ["Zapier workflows", "Email marketing automático", "Chatbots"],
      relatedTerms: ["Workflow", "Integração", "API"],
      seoKeywords: ["automação", "automatizar processos", "workflow automático", "eficiência"]
    },
    {
      id: 7,
      term: "API",
      definition: "Interface de Programação de Aplicações - conjunto de protocolos que permite que diferentes softwares se comuniquem entre si.",
      category: "Tecnologia",
      examples: ["API do ChatGPT", "API do Google", "REST API"],
      relatedTerms: ["Integração", "Webhook", "JSON"],
      seoKeywords: ["API", "interface programação", "integração sistemas", "webhook"]
    },
    {
      id: 8,
      term: "SEO",
      definition: "Search Engine Optimization - conjunto de técnicas para melhorar a visibilidade de um site nos resultados de busca orgânica.",
      category: "Marketing Digital",
      examples: ["Palavras-chave", "Meta descriptions", "Link building"],
      relatedTerms: ["SEM", "Marketing Digital", "Google Analytics"],
      seoKeywords: ["SEO", "otimização sites", "marketing digital", "google ranking"]
    },
    {
      id: 9,
      term: "Copywriting",
      definition: "Arte e ciência de escrever textos persuasivos com o objetivo de levar o leitor a realizar uma ação específica.",
      category: "Marketing Digital",
      examples: ["Headlines", "Call-to-action", "Email marketing"],
      relatedTerms: ["Marketing de Conteúdo", "Conversão", "Funil de Vendas"],
      seoKeywords: ["copywriting", "redação persuasiva", "textos vendas", "conversão"]
    },
    {
      id: 10,
      term: "Deep Learning",
      definition: "Subcampo do machine learning que usa redes neurais artificiais com múltiplas camadas para modelar e entender dados complexos.",
      category: "IA Geral",
      examples: ["Reconhecimento facial", "Tradução automática", "Geração de imagens"],
      relatedTerms: ["Redes Neurais", "Machine Learning", "TensorFlow"],
      seoKeywords: ["deep learning", "aprendizado profundo", "redes neurais", "IA avançada"]
    },
    {
      id: 11,
      term: "Freemium",
      definition: "Modelo de negócio que oferece serviços básicos gratuitos e funcionalidades premium pagas.",
      category: "Modelos de Negócio",
      examples: ["Canva", "Notion", "Grammarly"],
      relatedTerms: ["SaaS", "Monetização", "Planos de Assinatura"],
      seoKeywords: ["freemium", "modelo negócio", "gratuito premium", "saas"]
    },
    {
      id: 12,
      term: "Workflow",
      definition: "Sequência de processos ou tarefas organizadas para completar um objetivo específico de forma eficiente.",
      category: "Produtividade",
      examples: ["Aprovação de conteúdo", "Onboarding de clientes", "Processo de vendas"],
      relatedTerms: ["Automação", "Processo", "Eficiência"],
      seoKeywords: ["workflow", "fluxo trabalho", "processo automático", "produtividade"]
    },
    {
      id: 13,
      term: "Claude",
      definition: "Assistente de IA desenvolvido pela Anthropic, conhecido por suas capacidades avançadas de análise e conversação natural.",
      category: "Ferramentas IA",
      examples: ["Claude 3", "Claude Pro", "Anthropic AI"],
      relatedTerms: ["ChatGPT", "IA Conversacional", "Anthropic"],
      seoKeywords: ["claude ai", "anthropic", "assistente inteligência artificial", "claude 3"]
    },
    {
      id: 14,
      term: "Gemini",
      definition: "Modelo de IA multimodal do Google que pode processar texto, imagens, áudio e vídeo simultaneamente.",
      category: "Ferramentas IA",
      examples: ["Gemini Pro", "Bard", "Google AI"],
      relatedTerms: ["Google AI", "Multimodal", "LLM"],
      seoKeywords: ["gemini google", "google ai", "bard", "inteligência artificial google"]
    },
    {
      id: 15,
      term: "Prompt Engineering",
      definition: "Arte e ciência de criar instruções eficazes para sistemas de IA gerarem respostas precisas e úteis.",
      category: "Prompts",
      examples: ["Few-shot prompting", "Chain-of-thought", "Role prompting"],
      relatedTerms: ["Prompt", "IA Generativa", "Otimização"],
      seoKeywords: ["prompt engineering", "engenharia prompts", "otimização prompts", "técnicas prompt"]
    },
    {
      id: 16,
      term: "LLM (Large Language Model)",
      definition: "Modelo de linguagem de grande escala treinado em vastos conjuntos de dados textuais para compreender e gerar linguagem natural.",
      category: "IA Geral",
      examples: ["GPT-4", "Claude", "LLaMA", "PaLM"],
      relatedTerms: ["Transformer", "Neural Networks", "NLP"],
      seoKeywords: ["LLM", "large language model", "modelo linguagem", "GPT"]
    },
    {
      id: 17,
      term: "Midjourney",
      definition: "Ferramenta de IA para geração de imagens artísticas a partir de descrições textuais, conhecida por sua qualidade visual excepcional.",
      category: "Ferramentas IA",
      examples: ["Arte digital", "Concept art", "Ilustrações"],
      relatedTerms: ["DALL-E", "Stable Diffusion", "Arte IA"],
      seoKeywords: ["midjourney", "geração imagens ia", "arte artificial", "midjourney ai"]
    },
    {
      id: 18,
      term: "DALL-E",
      definition: "Sistema de IA da OpenAI que cria imagens realistas e artísticas a partir de descrições em linguagem natural.",
      category: "Ferramentas IA",
      examples: ["DALL-E 2", "DALL-E 3", "Geração de imagens"],
      relatedTerms: ["OpenAI", "Midjourney", "Stable Diffusion"],
      seoKeywords: ["dall-e", "openai imagens", "gerador imagens ia", "dall-e 3"]
    },
    {
      id: 19,
      term: "Stable Diffusion",
      definition: "Modelo de IA open-source para geração de imagens, permitindo uso gratuito e personalização avançada.",
      category: "Ferramentas IA",
      examples: ["Automatic1111", "ComfyUI", "DreamStudio"],
      relatedTerms: ["Open Source", "Diffusion Models", "Arte IA"],
      seoKeywords: ["stable diffusion", "ia open source", "geração imagens gratuita", "diffusion model"]
    },
    {
      id: 20,
      term: "Zapier",
      definition: "Plataforma de automação no-code que conecta diferentes aplicativos e serviços para criar workflows automatizados.",
      category: "No-Code",
      examples: ["Zaps", "Integrações", "Automação de tarefas"],
      relatedTerms: ["Automação", "Integração", "Workflow"],
      seoKeywords: ["zapier", "automação no-code", "integração aplicativos", "workflow automático"]
    },
    {
      id: 21,
      term: "Bubble",
      definition: "Plataforma no-code para desenvolvimento de aplicações web completas sem necessidade de programação.",
      category: "No-Code",
      examples: ["Web apps", "Marketplace", "SaaS"],
      relatedTerms: ["No-Code", "Desenvolvimento Visual", "Web Development"],
      seoKeywords: ["bubble", "desenvolvimento no-code", "criar aplicativo sem código", "bubble.io"]
    },
    {
      id: 22,
      term: "Webflow",
      definition: "Ferramenta de design e desenvolvimento web visual que permite criar sites responsivos sem código.",
      category: "No-Code",
      examples: ["Sites responsivos", "E-commerce", "CMS"],
      relatedTerms: ["Web Design", "CMS", "Responsive Design"],
      seoKeywords: ["webflow", "design web no-code", "criação sites", "webflow cms"]
    },
    {
      id: 23,
      term: "Airtable",
      definition: "Plataforma que combina a simplicidade de uma planilha com o poder de um banco de dados, ideal para organização e colaboração.",
      category: "Produtividade",
      examples: ["Base de dados", "CRM", "Gestão de projetos"],
      relatedTerms: ["Database", "Colaboração", "Organização"],
      seoKeywords: ["airtable", "banco dados visual", "planilha inteligente", "organização dados"]
    },
    {
      id: 24,
      term: "Notion",
      definition: "Workspace all-in-one que combina notas, documentos, wikis, bases de dados e gestão de projetos em uma única plataforma.",
      category: "Produtividade",
      examples: ["Documentação", "Wiki", "Gestão de projetos"],
      relatedTerms: ["Produtividade", "Colaboração", "Organização"],
      seoKeywords: ["notion", "workspace", "organização digital", "notion ai"]
    },
    {
      id: 25,
      term: "GitHub Copilot",
      definition: "Assistente de programação com IA que sugere código em tempo real, desenvolvido pela GitHub em parceria com OpenAI.",
      category: "Ferramentas IA",
      examples: ["Autocompletar código", "Sugestões IA", "Programação assistida"],
      relatedTerms: ["Programação", "GitHub", "OpenAI"],
      seoKeywords: ["github copilot", "programação ia", "assistente código", "copilot ai"]
    },
    {
      id: 26,
      term: "Canva",
      definition: "Plataforma de design gráfico online que democratiza a criação visual com templates e ferramentas intuitivas.",
      category: "Design",
      examples: ["Posts redes sociais", "Apresentações", "Logos"],
      relatedTerms: ["Design Gráfico", "Templates", "Marketing Visual"],
      seoKeywords: ["canva", "design gráfico online", "criação visual", "canva pro"]
    },
    {
      id: 27,
      term: "Figma",
      definition: "Ferramenta de design colaborativo baseada na web, amplamente usada para UI/UX design e prototipagem.",
      category: "Design",
      examples: ["UI Design", "Protótipos", "Design System"],
      relatedTerms: ["UI/UX", "Prototipagem", "Design Colaborativo"],
      seoKeywords: ["figma", "design ui ux", "prototipagem", "design colaborativo"]
    },
    {
      id: 28,
      term: "Grammarly",
      definition: "Assistente de escrita com IA que verifica gramática, ortografia, clareza e tom em textos em inglês.",
      category: "Ferramentas IA",
      examples: ["Correção gramatical", "Melhoria de texto", "Escrita profissional"],
      relatedTerms: ["Escrita", "Correção", "Produtividade"],
      seoKeywords: ["grammarly", "corretor inglês", "assistente escrita", "gramática ia"]
    },
    {
      id: 29,
      term: "Jasper AI",
      definition: "Plataforma de IA especializada em criação de conteúdo de marketing, copywriting e textos comerciais.",
      category: "Ferramentas IA",
      examples: ["Copy de vendas", "Conteúdo blog", "Email marketing"],
      relatedTerms: ["Copywriting", "Marketing de Conteúdo", "IA Generativa"],
      seoKeywords: ["jasper ai", "copywriting ia", "criação conteúdo", "jasper"]
    },
    {
      id: 30,
      term: "Copy.ai",
      definition: "Ferramenta de IA focada em copywriting e criação de textos persuasivos para marketing e vendas.",
      category: "Ferramentas IA",
      examples: ["Headlines", "Descrições de produtos", "Anúncios"],
      relatedTerms: ["Copywriting", "Marketing", "Vendas"],
      seoKeywords: ["copy.ai", "copywriting automático", "textos vendas ia", "copy ai"]
    },
    {
      id: 31,
      term: "OpenAI",
      definition: "Empresa de pesquisa em IA responsável pelo desenvolvimento do ChatGPT, GPT-4, DALL-E e outras tecnologias revolucionárias.",
      category: "IA Geral",
      examples: ["ChatGPT", "GPT-4", "DALL-E", "Whisper"],
      relatedTerms: ["ChatGPT", "GPT", "IA Generativa"],
      seoKeywords: ["openai", "chatgpt empresa", "gpt-4", "openai api"]
    },
    {
      id: 32,
      term: "Transformer",
      definition: "Arquitetura de rede neural que revolucionou o processamento de linguagem natural e é base dos modelos GPT.",
      category: "IA Geral",
      examples: ["BERT", "GPT", "T5", "Attention Mechanism"],
      relatedTerms: ["Neural Networks", "Attention", "NLP"],
      seoKeywords: ["transformer", "arquitetura neural", "attention mechanism", "bert gpt"]
    },
    {
      id: 33,
      term: "NLP (Natural Language Processing)",
      definition: "Campo da IA que se concentra na interação entre computadores e linguagem humana natural.",
      category: "IA Geral",
      examples: ["Análise de sentimento", "Tradução automática", "Chatbots"],
      relatedTerms: ["Machine Learning", "Linguística Computacional", "IA"],
      seoKeywords: ["nlp", "processamento linguagem natural", "análise texto", "linguística computacional"]
    },
    {
      id: 34,
      term: "Computer Vision",
      definition: "Campo da IA que treina computadores para interpretar e compreender informações visuais do mundo real.",
      category: "IA Geral",
      examples: ["Reconhecimento facial", "Detecção de objetos", "Análise de imagens"],
      relatedTerms: ["Deep Learning", "CNN", "Visão Computacional"],
      seoKeywords: ["computer vision", "visão computacional", "reconhecimento imagem", "análise visual ia"]
    },
    {
      id: 35,
      term: "Chatbot",
      definition: "Programa de computador projetado para simular conversas com usuários humanos, especialmente na internet.",
      category: "IA Geral",
      examples: ["Atendimento ao cliente", "FAQ automático", "Assistentes virtuais"],
      relatedTerms: ["IA Conversacional", "Automação", "Atendimento"],
      seoKeywords: ["chatbot", "bot conversação", "atendimento automático", "assistente virtual"]
    },
    {
      id: 36,
      term: "RPA (Robotic Process Automation)",
      definition: "Tecnologia que usa software para automatizar tarefas repetitivas e baseadas em regras nos negócios.",
      category: "Automação",
      examples: ["Processamento de faturas", "Entrada de dados", "Relatórios automáticos"],
      relatedTerms: ["Automação", "Processo", "Eficiência"],
      seoKeywords: ["rpa", "automação processos", "robotic process automation", "automação robótica"]
    },
    {
      id: 37,
      term: "SaaS (Software as a Service)",
      definition: "Modelo de distribuição de software onde aplicações são hospedadas na nuvem e acessadas via internet.",
      category: "Modelos de Negócio",
      examples: ["Google Workspace", "Salesforce", "Slack"],
      relatedTerms: ["Cloud Computing", "Assinatura", "Software"],
      seoKeywords: ["saas", "software como serviço", "aplicativo nuvem", "assinatura software"]
    },
    {
      id: 38,
      term: "API (Application Programming Interface)",
      definition: "Conjunto de protocolos e ferramentas que permite que diferentes softwares se comuniquem entre si.",
      category: "Tecnologia",
      examples: ["REST API", "GraphQL", "Webhook"],
      relatedTerms: ["Integração", "Desenvolvimento", "Protocolo"],
      seoKeywords: ["api", "interface programação", "integração sistemas", "rest api"]
    },
    {
      id: 39,
      term: "Cloud Computing",
      definition: "Fornecimento de serviços de computação através da internet, incluindo servidores, armazenamento e aplicações.",
      category: "Tecnologia",
      examples: ["AWS", "Google Cloud", "Microsoft Azure"],
      relatedTerms: ["SaaS", "Infraestrutura", "Escalabilidade"],
      seoKeywords: ["cloud computing", "computação nuvem", "aws", "google cloud"]
    },
    {
      id: 40,
      term: "Big Data",
      definition: "Conjuntos de dados extremamente grandes e complexos que requerem ferramentas especializadas para processamento e análise.",
      category: "Análise de Dados",
      examples: ["Analytics", "Data Mining", "Business Intelligence"],
      relatedTerms: ["Data Science", "Analytics", "Machine Learning"],
      seoKeywords: ["big data", "análise dados", "data science", "business intelligence"]
    },
    {
      id: 41,
      term: "Data Science",
      definition: "Campo interdisciplinar que usa métodos científicos, processos e sistemas para extrair conhecimento de dados.",
      category: "Análise de Dados",
      examples: ["Análise preditiva", "Visualização de dados", "Modelagem estatística"],
      relatedTerms: ["Big Data", "Machine Learning", "Estatística"],
      seoKeywords: ["data science", "ciência dados", "análise preditiva", "cientista dados"]
    },
    {
      id: 42,
      term: "Business Intelligence",
      definition: "Conjunto de tecnologias e estratégias para análise de dados empresariais e apoio à tomada de decisões.",
      category: "Análise de Dados",
      examples: ["Dashboards", "Relatórios", "KPIs"],
      relatedTerms: ["Analytics", "Data Visualization", "Reporting"],
      seoKeywords: ["business intelligence", "bi", "inteligência negócios", "dashboard empresarial"]
    },
    {
      id: 43,
      term: "KPI (Key Performance Indicator)",
      definition: "Métricas quantificáveis usadas para avaliar o sucesso de uma organização ou atividade específica.",
      category: "Análise de Dados",
      examples: ["Taxa de conversão", "ROI", "Satisfação do cliente"],
      relatedTerms: ["Métricas", "Performance", "Analytics"],
      seoKeywords: ["kpi", "indicadores performance", "métricas negócio", "key performance indicator"]
    },
    {
      id: 44,
      term: "CRM (Customer Relationship Management)",
      definition: "Sistema para gerenciar interações e relacionamentos de uma empresa com clientes atuais e potenciais.",
      category: "Marketing Digital",
      examples: ["Salesforce", "HubSpot", "Pipedrive"],
      relatedTerms: ["Vendas", "Marketing", "Relacionamento"],
      seoKeywords: ["crm", "gestão relacionamento cliente", "customer relationship", "sistema vendas"]
    },
    {
      id: 45,
      term: "CMS (Content Management System)",
      definition: "Software que permite criar, gerenciar e modificar conteúdo digital sem conhecimento técnico especializado.",
      category: "Tecnologia",
      examples: ["WordPress", "Drupal", "Webflow CMS"],
      relatedTerms: ["Website", "Conteúdo", "Publicação"],
      seoKeywords: ["cms", "sistema gerenciamento conteúdo", "wordpress", "content management"]
    }
  ];

  // Filter terms based on search and letter
  const filteredTerms = glossaryTerms.filter(term => {
    const matchesSearch = term.term.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         term.definition.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         term.seoKeywords.some(keyword => keyword.toLowerCase().includes(searchTerm.toLowerCase()));
    
    const matchesLetter = selectedLetter === "all" || 
                         term.term.charAt(0).toLowerCase() === selectedLetter.toLowerCase();
    
    return matchesSearch && matchesLetter;
  });

  // Get unique first letters for alphabet navigation
  const availableLetters = Array.from(new Set(
    glossaryTerms.map(term => term.term.charAt(0).toUpperCase())
  )).sort();

  const categories = Array.from(new Set(glossaryTerms.map(term => term.category)));

  return (
    <div className="min-h-screen bg-gradient-to-br from-indigo-50 via-white to-cyan-50">
      <SEOHead
        title="Glossário de IA e Tecnologia - 45+ Termos Essenciais | Prompt Genius AI"
        description="Dicionário completo de Inteligência Artificial com 45+ termos: ChatGPT, Prompt Engineering, Machine Learning, No-Code e muito mais. Guia definitivo em português."
        keywords={[
          "glossário ia", "dicionário inteligência artificial", "termos ia", "chatgpt significado",
          "prompt engineering", "machine learning", "deep learning", "no-code", "automação",
          "midjourney", "claude ai", "gemini", "openai", "transformer", "nlp", "computer vision",
          "api", "saas", "crm", "cms", "big data", "data science", "business intelligence"
        ]}
        structuredData={{
          "@context": "https://schema.org",
          "@type": "DefinedTermSet",
          "name": "Glossário de IA e Tecnologia",
          "description": "Dicionário completo de termos de Inteligência Artificial e tecnologia",
          "hasDefinedTerm": glossaryTerms.map(term => ({
            "@type": "DefinedTerm",
            "name": term.term,
            "description": term.definition,
            "inDefinedTermSet": "Glossário de IA e Tecnologia"
          }))
        }}
      />
      <Header />
      
      {/* Hero Section */}
      <section className="py-16 px-4">
        <div className="max-w-7xl mx-auto text-center">
          <div className="mb-6">
            <BookOpen className="h-16 w-16 mx-auto text-indigo-600 mb-4" />
          </div>
          <h1 className="text-4xl md:text-6xl font-bold text-gray-900 mb-6">
            Glossário de IA e Tecnologia
            <span className="block text-transparent bg-clip-text bg-gradient-to-r from-indigo-600 to-cyan-600">
              Dicionário Completo
            </span>
          </h1>
          <p className="text-xl text-gray-600 mb-8 max-w-3xl mx-auto">
            Entenda todos os termos essenciais sobre Inteligência Artificial, No-Code, 
            Marketing Digital e tecnologia. Seu guia definitivo para dominar o vocabulário tech.
          </p>
        </div>
      </section>

      {/* Search */}
      <section className="py-8 px-4">
        <div className="max-w-4xl mx-auto">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-5 w-5" />
            <Input
              placeholder="Buscar termos no glossário..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10 h-12 text-lg"
            />
          </div>
        </div>
      </section>

      {/* Alphabet Navigation */}
      <section className="py-4 px-4">
        <div className="max-w-7xl mx-auto">
          <div className="flex flex-wrap justify-center gap-2 mb-8">
            <button
              onClick={() => setSelectedLetter("all")}
              className={`px-3 py-1 rounded-md text-sm font-medium transition-colors ${
                selectedLetter === "all" 
                  ? "bg-indigo-600 text-white" 
                  : "bg-gray-100 text-gray-700 hover:bg-gray-200"
              }`}
            >
              Todos
            </button>
            {availableLetters.map(letter => (
              <button
                key={letter}
                onClick={() => setSelectedLetter(letter)}
                className={`px-3 py-1 rounded-md text-sm font-medium transition-colors ${
                  selectedLetter === letter 
                    ? "bg-indigo-600 text-white" 
                    : "bg-gray-100 text-gray-700 hover:bg-gray-200"
                }`}
              >
                {letter}
              </button>
            ))}
          </div>
        </div>
      </section>

      {/* Terms */}
      <section className="py-8 px-4">
        <div className="max-w-7xl mx-auto">
          <div className="flex justify-between items-center mb-6">
            <h2 className="text-2xl font-bold text-gray-900">
              {filteredTerms.length} termos encontrados
            </h2>
            <div className="text-sm text-gray-500">
              Ordenado alfabeticamente
            </div>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {filteredTerms.map((term) => (
              <Card key={term.id} className="hover:shadow-lg transition-shadow">
                <CardHeader>
                  <div className="flex justify-between items-start mb-2">
                    <Badge variant="secondary" className="text-xs">
                      {term.category}
                    </Badge>
                    <Hash className="h-4 w-4 text-gray-400" />
                  </div>
                  <CardTitle className="text-xl text-indigo-600">
                    {term.term}
                  </CardTitle>
                  <CardDescription className="text-base leading-relaxed">
                    {term.definition}
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  {term.examples && term.examples.length > 0 && (
                    <div className="mb-4">
                      <h4 className="font-semibold text-sm text-gray-700 mb-2">Exemplos:</h4>
                      <div className="flex flex-wrap gap-1">
                        {term.examples.map((example, index) => (
                          <Badge key={index} variant="outline" className="text-xs">
                            {example}
                          </Badge>
                        ))}
                      </div>
                    </div>
                  )}

                  {term.relatedTerms && term.relatedTerms.length > 0 && (
                    <div className="mb-4">
                      <h4 className="font-semibold text-sm text-gray-700 mb-2">Termos Relacionados:</h4>
                      <div className="flex flex-wrap gap-1">
                        {term.relatedTerms.map((relatedTerm, index) => (
                          <Badge key={index} variant="outline" className="text-xs bg-indigo-50 text-indigo-700">
                            {relatedTerm}
                          </Badge>
                        ))}
                      </div>
                    </div>
                  )}

                  <div className="text-xs text-gray-500">
                    <strong>Palavras-chave SEO:</strong> {term.seoKeywords.join(", ")}
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>

          {filteredTerms.length === 0 && (
            <div className="text-center py-12">
              <div className="text-gray-400 mb-4">
                <Search className="h-16 w-16 mx-auto" />
              </div>
              <h3 className="text-xl font-semibold text-gray-900 mb-2">
                Nenhum termo encontrado
              </h3>
              <p className="text-gray-600">
                Tente ajustar seu termo de busca ou navegar pelas letras
              </p>
            </div>
          )}
        </div>
      </section>

      {/* SEO Footer */}
      <section className="py-12 px-4 bg-gray-50">
        <div className="max-w-7xl mx-auto text-center">
          <h3 className="text-2xl font-bold text-gray-900 mb-4">
            Glossário Completo de IA e Tecnologia
          </h3>
          <p className="text-gray-600 max-w-3xl mx-auto">
            Este glossário contém os principais termos sobre Inteligência Artificial, No-Code, 
            Marketing Digital, SEO, Automação e tecnologia em geral. Ideal para iniciantes 
            e profissionais que querem dominar o vocabulário tech em português.
          </p>
        </div>
      </section>

      <Footer />
    </div>
  );
};

export default Glossario;
