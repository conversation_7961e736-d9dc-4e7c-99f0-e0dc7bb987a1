import { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>h, <PERSON>R<PERSON> } from "lucide-react";
import { Input } from "@/components/ui/input";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import Header from "@/components/Header";

interface GlossaryTerm {
  id: number;
  term: string;
  definition: string;
  category: string;
  examples?: string[];
  relatedTerms?: string[];
  seoKeywords: string[];
}

const Glossario = () => {
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedLetter, setSelectedLetter] = useState("all");

  // Comprehensive glossary for SEO - AI and tech terms in Portuguese
  const glossaryTerms: GlossaryTerm[] = [
    {
      id: 1,
      term: "Inteligência Artificial (IA)",
      definition: "Tecnologia que permite que máquinas simulem a inteligência humana, incluindo aprendizado, raciocínio e autocorreção.",
      category: "IA Geral",
      examples: ["ChatGPT", "Claude", "Gemini"],
      relatedTerms: ["Machine Learning", "Deep Learning", "Redes Neurais"],
      seoKeywords: ["inteligência artificial", "IA", "artificial intelligence", "tecnologia IA"]
    },
    {
      id: 2,
      term: "Prompt",
      definition: "Instrução ou comando dado a uma IA para gerar uma resposta específica. É a forma de comunicação entre humanos e sistemas de IA.",
      category: "Prompts",
      examples: ["Escreva um artigo sobre...", "Crie uma lista de...", "Analise este texto..."],
      relatedTerms: ["Prompt Engineering", "ChatGPT", "IA Generativa"],
      seoKeywords: ["prompt", "prompt IA", "comando IA", "instrução artificial"]
    },
    {
      id: 3,
      term: "Machine Learning",
      definition: "Subcampo da IA que permite que sistemas aprendam e melhorem automaticamente através da experiência sem serem explicitamente programados.",
      category: "IA Geral",
      examples: ["Algoritmos de recomendação", "Reconhecimento de imagem", "Processamento de linguagem natural"],
      relatedTerms: ["Deep Learning", "Algoritmos", "Dados de Treinamento"],
      seoKeywords: ["machine learning", "aprendizado de máquina", "ML", "algoritmos inteligentes"]
    },
    {
      id: 4,
      term: "No-Code",
      definition: "Abordagem de desenvolvimento que permite criar aplicações sem escrever código, usando interfaces visuais e ferramentas drag-and-drop.",
      category: "No-Code",
      examples: ["Bubble", "Webflow", "Zapier", "Airtable"],
      relatedTerms: ["Low-Code", "Desenvolvimento Visual", "Automação"],
      seoKeywords: ["no-code", "sem código", "desenvolvimento visual", "ferramentas no-code"]
    },
    {
      id: 5,
      term: "ChatGPT",
      definition: "Modelo de linguagem desenvolvido pela OpenAI, capaz de gerar texto semelhante ao humano e manter conversas naturais.",
      category: "Ferramentas IA",
      examples: ["GPT-3.5", "GPT-4", "ChatGPT Plus"],
      relatedTerms: ["OpenAI", "LLM", "IA Conversacional"],
      seoKeywords: ["chatgpt", "openai", "gpt", "chat inteligência artificial"]
    },
    {
      id: 6,
      term: "Automação",
      definition: "Processo de usar tecnologia para executar tarefas com mínima intervenção humana, aumentando eficiência e reduzindo erros.",
      category: "Automação",
      examples: ["Zapier workflows", "Email marketing automático", "Chatbots"],
      relatedTerms: ["Workflow", "Integração", "API"],
      seoKeywords: ["automação", "automatizar processos", "workflow automático", "eficiência"]
    },
    {
      id: 7,
      term: "API",
      definition: "Interface de Programação de Aplicações - conjunto de protocolos que permite que diferentes softwares se comuniquem entre si.",
      category: "Tecnologia",
      examples: ["API do ChatGPT", "API do Google", "REST API"],
      relatedTerms: ["Integração", "Webhook", "JSON"],
      seoKeywords: ["API", "interface programação", "integração sistemas", "webhook"]
    },
    {
      id: 8,
      term: "SEO",
      definition: "Search Engine Optimization - conjunto de técnicas para melhorar a visibilidade de um site nos resultados de busca orgânica.",
      category: "Marketing Digital",
      examples: ["Palavras-chave", "Meta descriptions", "Link building"],
      relatedTerms: ["SEM", "Marketing Digital", "Google Analytics"],
      seoKeywords: ["SEO", "otimização sites", "marketing digital", "google ranking"]
    },
    {
      id: 9,
      term: "Copywriting",
      definition: "Arte e ciência de escrever textos persuasivos com o objetivo de levar o leitor a realizar uma ação específica.",
      category: "Marketing Digital",
      examples: ["Headlines", "Call-to-action", "Email marketing"],
      relatedTerms: ["Marketing de Conteúdo", "Conversão", "Funil de Vendas"],
      seoKeywords: ["copywriting", "redação persuasiva", "textos vendas", "conversão"]
    },
    {
      id: 10,
      term: "Deep Learning",
      definition: "Subcampo do machine learning que usa redes neurais artificiais com múltiplas camadas para modelar e entender dados complexos.",
      category: "IA Geral",
      examples: ["Reconhecimento facial", "Tradução automática", "Geração de imagens"],
      relatedTerms: ["Redes Neurais", "Machine Learning", "TensorFlow"],
      seoKeywords: ["deep learning", "aprendizado profundo", "redes neurais", "IA avançada"]
    },
    {
      id: 11,
      term: "Freemium",
      definition: "Modelo de negócio que oferece serviços básicos gratuitos e funcionalidades premium pagas.",
      category: "Modelos de Negócio",
      examples: ["Canva", "Notion", "Grammarly"],
      relatedTerms: ["SaaS", "Monetização", "Planos de Assinatura"],
      seoKeywords: ["freemium", "modelo negócio", "gratuito premium", "saas"]
    },
    {
      id: 12,
      term: "Workflow",
      definition: "Sequência de processos ou tarefas organizadas para completar um objetivo específico de forma eficiente.",
      category: "Produtividade",
      examples: ["Aprovação de conteúdo", "Onboarding de clientes", "Processo de vendas"],
      relatedTerms: ["Automação", "Processo", "Eficiência"],
      seoKeywords: ["workflow", "fluxo trabalho", "processo automático", "produtividade"]
    }
  ];

  // Filter terms based on search and letter
  const filteredTerms = glossaryTerms.filter(term => {
    const matchesSearch = term.term.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         term.definition.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         term.seoKeywords.some(keyword => keyword.toLowerCase().includes(searchTerm.toLowerCase()));
    
    const matchesLetter = selectedLetter === "all" || 
                         term.term.charAt(0).toLowerCase() === selectedLetter.toLowerCase();
    
    return matchesSearch && matchesLetter;
  });

  // Get unique first letters for alphabet navigation
  const availableLetters = Array.from(new Set(
    glossaryTerms.map(term => term.term.charAt(0).toUpperCase())
  )).sort();

  const categories = Array.from(new Set(glossaryTerms.map(term => term.category)));

  return (
    <div className="min-h-screen bg-gradient-to-br from-indigo-50 via-white to-cyan-50">
      <Header />
      
      {/* Hero Section */}
      <section className="py-16 px-4">
        <div className="max-w-7xl mx-auto text-center">
          <div className="mb-6">
            <BookOpen className="h-16 w-16 mx-auto text-indigo-600 mb-4" />
          </div>
          <h1 className="text-4xl md:text-6xl font-bold text-gray-900 mb-6">
            Glossário de IA e Tecnologia
            <span className="block text-transparent bg-clip-text bg-gradient-to-r from-indigo-600 to-cyan-600">
              Dicionário Completo
            </span>
          </h1>
          <p className="text-xl text-gray-600 mb-8 max-w-3xl mx-auto">
            Entenda todos os termos essenciais sobre Inteligência Artificial, No-Code, 
            Marketing Digital e tecnologia. Seu guia definitivo para dominar o vocabulário tech.
          </p>
        </div>
      </section>

      {/* Search */}
      <section className="py-8 px-4">
        <div className="max-w-4xl mx-auto">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-5 w-5" />
            <Input
              placeholder="Buscar termos no glossário..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10 h-12 text-lg"
            />
          </div>
        </div>
      </section>

      {/* Alphabet Navigation */}
      <section className="py-4 px-4">
        <div className="max-w-7xl mx-auto">
          <div className="flex flex-wrap justify-center gap-2 mb-8">
            <button
              onClick={() => setSelectedLetter("all")}
              className={`px-3 py-1 rounded-md text-sm font-medium transition-colors ${
                selectedLetter === "all" 
                  ? "bg-indigo-600 text-white" 
                  : "bg-gray-100 text-gray-700 hover:bg-gray-200"
              }`}
            >
              Todos
            </button>
            {availableLetters.map(letter => (
              <button
                key={letter}
                onClick={() => setSelectedLetter(letter)}
                className={`px-3 py-1 rounded-md text-sm font-medium transition-colors ${
                  selectedLetter === letter 
                    ? "bg-indigo-600 text-white" 
                    : "bg-gray-100 text-gray-700 hover:bg-gray-200"
                }`}
              >
                {letter}
              </button>
            ))}
          </div>
        </div>
      </section>

      {/* Terms */}
      <section className="py-8 px-4">
        <div className="max-w-7xl mx-auto">
          <div className="flex justify-between items-center mb-6">
            <h2 className="text-2xl font-bold text-gray-900">
              {filteredTerms.length} termos encontrados
            </h2>
            <div className="text-sm text-gray-500">
              Ordenado alfabeticamente
            </div>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {filteredTerms.map((term) => (
              <Card key={term.id} className="hover:shadow-lg transition-shadow">
                <CardHeader>
                  <div className="flex justify-between items-start mb-2">
                    <Badge variant="secondary" className="text-xs">
                      {term.category}
                    </Badge>
                    <Hash className="h-4 w-4 text-gray-400" />
                  </div>
                  <CardTitle className="text-xl text-indigo-600">
                    {term.term}
                  </CardTitle>
                  <CardDescription className="text-base leading-relaxed">
                    {term.definition}
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  {term.examples && term.examples.length > 0 && (
                    <div className="mb-4">
                      <h4 className="font-semibold text-sm text-gray-700 mb-2">Exemplos:</h4>
                      <div className="flex flex-wrap gap-1">
                        {term.examples.map((example, index) => (
                          <Badge key={index} variant="outline" className="text-xs">
                            {example}
                          </Badge>
                        ))}
                      </div>
                    </div>
                  )}

                  {term.relatedTerms && term.relatedTerms.length > 0 && (
                    <div className="mb-4">
                      <h4 className="font-semibold text-sm text-gray-700 mb-2">Termos Relacionados:</h4>
                      <div className="flex flex-wrap gap-1">
                        {term.relatedTerms.map((relatedTerm, index) => (
                          <Badge key={index} variant="outline" className="text-xs bg-indigo-50 text-indigo-700">
                            {relatedTerm}
                          </Badge>
                        ))}
                      </div>
                    </div>
                  )}

                  <div className="text-xs text-gray-500">
                    <strong>Palavras-chave SEO:</strong> {term.seoKeywords.join(", ")}
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>

          {filteredTerms.length === 0 && (
            <div className="text-center py-12">
              <div className="text-gray-400 mb-4">
                <Search className="h-16 w-16 mx-auto" />
              </div>
              <h3 className="text-xl font-semibold text-gray-900 mb-2">
                Nenhum termo encontrado
              </h3>
              <p className="text-gray-600">
                Tente ajustar seu termo de busca ou navegar pelas letras
              </p>
            </div>
          )}
        </div>
      </section>

      {/* SEO Footer */}
      <section className="py-12 px-4 bg-gray-50">
        <div className="max-w-7xl mx-auto text-center">
          <h3 className="text-2xl font-bold text-gray-900 mb-4">
            Glossário Completo de IA e Tecnologia
          </h3>
          <p className="text-gray-600 max-w-3xl mx-auto">
            Este glossário contém os principais termos sobre Inteligência Artificial, No-Code, 
            Marketing Digital, SEO, Automação e tecnologia em geral. Ideal para iniciantes 
            e profissionais que querem dominar o vocabulário tech em português.
          </p>
        </div>
      </section>
    </div>
  );
};

export default Glossario;
